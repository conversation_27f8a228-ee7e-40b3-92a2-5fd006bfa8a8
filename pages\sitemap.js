import React from "react";
import <PERSON> from "next/link";
import common from "@/styles/comon.module.scss";
import Banner from "@/component/banner/Banner";
const becomeMember = () => {
  return (
    <div>
      <Banner backgroundImg={"/images/news-detail-banner.jpg"} banner_text={""} />

 
      <section className={`${common.d_flex}  ${common.side_line}   ${common.pb_100} ${common.mobile_padding_tb}  `} >
        <div className={`${common.container} ${common.container_news}`}>

       <ul>
        <li><Link target="_blank" href={"/"}>home</Link></li>
        <li><Link target="_blank" href={"become-member"}>become-member</Link></li>
        <li><Link target="_blank" href={"corporate-events"}>corporate-events</Link></li>
        <li><Link target="_blank" href={"experience-rgc"}>experience-rgc</Link></li>
        <li><Link target="_blank" href={"learn-to-play-academy"}>learn-to-play-academy</Link></li>
        <li><Link target="_blank" href={"membership"}>membership</Link></li>
         <li><Link target="_blank" href={"news-detail"}>news-detail</Link></li>
        <li><Link target="_blank" href={"news"}>news</Link></li>
        <li><Link target="_blank" href={"tournament"}>tournament</Link></li>
       </ul>
        </div>
      </section>
    </div>
  );
};

export default becomeMember;
