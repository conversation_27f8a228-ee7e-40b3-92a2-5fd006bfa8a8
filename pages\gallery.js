import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import Banner from "@/component/banner/Banner";
import "@fancyapps/ui/dist/fancybox/fancybox.css";
import { Fancybox } from "@fancyapps/ui";


import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getThemeoptions, getGallerypage } from "@/utils/lib/server/publicServices";
import { useRouter } from 'next/router';

const becomeMember = (props) => {
  useEffect(() => {
    Fancybox.bind("[data-fancybox='gallery']", {
      Thumbs: {
        autoStart: true,
      },
      arrows: true,
      groupAll: true,
    });

    return () => {
      Fancybox.destroy();
    };
  }, []);

  const route = useRouter()
     const pathname = route.pathname
     const yoastData = props?.GalleryData?.yoast_head_json;
  
    if (!props?.GalleryData) {
      return null;
    }
  const [visiblePosts, setVisiblePosts] = useState(16); // Initial visible posts
  const loadMorePosts = () => {
    setVisiblePosts((prev) => prev + 16); // Load 5 more posts
  };

  return (
    
    <div>
      {yoastData && <Yoast meta={yoastData} />}
            
      {props &&
        props?.GalleryData &&
        props?.GalleryData?.acf &&
        (props?.GalleryData?.acf?.banner_image || props?.GalleryData?.title || props?.GalleryData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.GalleryData?.acf?.banner_image?.url}
                banner_text={parse(props?.GalleryData?.acf?.banner_title || props?.GalleryData?.title?.rendered)}
            />
      ) : null}
      
            {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.news_menu &&
        <section className={`${common.tab_section} ${common.mediacenter_tab_section} `}>
          <div className={`${common.container}`}>
            <ul className={`${common.container_tab}`}>
              {props?.IRMenuData?.news_menu &&
                props?.IRMenuData?.news_menu.map((data, irindex) => (
                <li key={irindex}>
                  <Link                      
                      className={`${pathname == data?.learn_menu?.url ? `${common.active_tab}` : ''}`}
                      target={data?.learn_menu?.target}
                      href={data.learn_menu?.url}
                  >
                    {data?.learn_menu?.title && parse(data?.learn_menu?.title)}
                  </Link>
                </li>
              ))}  
            </ul>
          </div>
        </section>
      }

      {/* ---------------- */}
      {props &&
       props?.GalleryData &&
       props?.GalleryData?.acf &&
       props?.GalleryData?.acf?.gallery &&
        <section
          className={`${common.d_flex} ${common.tab_side_line} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}`}
        >
          <div className={`${common.container} ${common.container_gallery}`}>
            {props?.GalleryData?.acf?.gallery &&
              <ul className={common.gallery_grid_new}>
                {props?.GalleryData?.acf?.gallery &&
                  props?.GalleryData?.acf?.gallery.slice(0,visiblePosts).map((galleryimg, gindex) => (
                    // props?.GalleryData?.acf?.gallery.map((galleryimg, gindex) => (
                    <li key={gindex} data-aos="fade-up" data-aos-duration="700">
                      <a
                        data-fancybox="gallery"
                        href={galleryimg?.url}
                        className={`${common.image_block_style}  ${common.p_relative}  ${common.h_dining_block} ${common.fill_image_wrapper}`}
                      >
                        <Image
                          src={galleryimg?.url}
                          fill
                          style={{ objectFit: "cover" }}
                          alt="image"
                          sizes="(max-width: 768px) 100vw"
                        />

                        <Image
                          className={`${common.w_100} ${common.holder}`}
                          src="/images/holder_9a.jpg"
                          width={672}
                          height={447}
                          alt="button image"
                          style={{
                            height: "auto",
                            width: "100%",
                            display: "block",
                          }}
                        />
                      </a>
                    </li>
                  ))}
            
              </ul>
            }

            {visiblePosts < props?.GalleryData?.acf?.gallery?.length && (
              <a onClick={loadMorePosts}
                className={`${common.btn_read} ${common.mt_80} ${common.trans} ${common.load_more} `}
              >
                {route.locale === "en" ? "Load More..." : "تحميل المزيد ..."}
              </a>
            )}
            
          </div>
        </section>
      }
    </div>
  );
};

export default becomeMember;



export const getStaticProps = async (locale) => {
// const { getHome } = await usePublicServices();
    const GalleryData = await getGallerypage(locale); 
    const IRMenuData = await getThemeoptions(locale.locale);     
  
    
  return {
    props: {
          GalleryData: GalleryData || null,  
          IRMenuData: IRMenuData || null,  
         
    },
    revalidate: 10,
  };
};