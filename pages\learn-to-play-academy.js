import React, { useState } from 'react';
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import Banner from "@/component/banner/Banner";
import EnquireModal from '@/component/enquireModal'
import EnquireAcademy from '@/component/enquireAcademy'

import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getAcademypage, getThemeoptions } from "@/utils/lib/server/publicServices";
import { useRouter } from 'next/router';


const becomeMember = (props) => {
  
   const route = useRouter()
  const pathname = route.pathname
  

  // const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [modalData, setModalData] = useState(null);
  const [open, setOpen] = useState(false);
  
 const [selectedPersons, setSelectedPersons] = useState(props?.AcademyData?.acf?.no_of_persons[0]?.persons_number || "");
 const [selectedDuration, setSelectedDuration] = useState(props?.AcademyData?.acf?.duration_lists[0]?.list_of_durations || "");
  const [calculatedPrice, setCalculatedPrice] = useState(props?.AcademyData?.acf?.price_selection[0]?.price || "1000");
  
  const handleOpen = (index, section) => {
  let selectedData = null;
  if (section === "premium") {
    selectedData = props?.AcademyData?.acf?.coaching_programs[index];
  } else if (section === "midweek") {
    selectedData = props?.AcademyData?.acf?.coaching_packages[index];
  }
//  console.log("Selected Data:", selectedData); // Debugging
  setModalData(selectedData);
  setOpen(true);
};

    const yoastData = props?.AcademyData?.yoast_head_json;

  // Function to calculate price
  const findPrice = (persons, duration) => {
    if (!persons || !duration) return "";
    const matchingPrice = props?.AcademyData?.acf?.price_selection.find(
      (item) => item.no_of_persons === persons && item.duration === duration
    );
    return matchingPrice ? matchingPrice.price : "Not Available";
  };


  const handlePersonsChange = (e) => {
    const persons = e.target.value;
    setSelectedPersons(persons);

    // Recalculate price if duration is also selected
    if (selectedDuration) {
      const price = findPrice(persons, selectedDuration);
      setCalculatedPrice(price);
    }
  };

  const handleDurationChange = (e) => {
    const duration = e.target.value;
    setSelectedDuration(duration);

    // Recalculate price if persons are also selected
    if (selectedPersons) {
      const price = findPrice(selectedPersons, duration);
      setCalculatedPrice(price);
    }
  };
  
  if (!props?.AcademyData) {
        return null;
  }

const [open2, setOpen2] = useState(false);
const handleOpen2 = () => setOpen2(true);
const handleClose2 = () => setOpen2(false);
  
  return (
    <div>
       {yoastData && <Yoast meta={yoastData} />}
      
      <EnquireAcademy open={open2} onClose={handleClose2} Formfeild={props?.AcademyData?.acf} personsdata={selectedPersons} duration={selectedDuration} price={calculatedPrice} />
      
      <EnquireModal open={open} onClose={handleClose} data={modalData} Formfeild={props?.AcademyData?.acf} />
      
      
      
      
      {props &&
        props?.AcademyData &&
        props?.AcademyData?.acf &&
        (props?.AcademyData?.acf?.banner_image || props?.AcademyData?.title || props?.AcademyData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.AcademyData?.acf?.banner_image?.url}
                banner_text={parse(props?.AcademyData?.acf?.banner_title || props?.AcademyData?.title?.rendered)}
            />
      ) : null}

       {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.learn_to_play_menu &&
        <section className={`${common.tab_section}`}>
          <div className={`${common.container}`}>
            <ul className={`${common.container_tab}`}>
              {props?.IRMenuData?.learn_to_play_menu &&
                props?.IRMenuData?.learn_to_play_menu.map((data, irindex) => (
                <li key={irindex}>
                  <Link                      
                      className={`${pathname == data?.learn_menu?.url ? `${common.active_tab}` : ''}`}
                      target={data?.learn_menu?.target}
                      href={data.learn_menu?.url}
                  >
                    {data?.learn_menu?.title && parse(data?.learn_menu?.title)}
                  </Link>
                </li>
              ))}  
            </ul>
          </div>
        </section>
      }
       {props &&
        props?.AcademyData &&
        props?.AcademyData?.acf &&
        (props?.AcademyData?.acf?.overview_title ||
          props?.AcademyData?.acf?.overview_content ||
          props?.AcademyData?.acf?.overview_image) ? (
      <section
        className={`${common.d_flex} ${common.tab_side_line} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
      >
        <div className={`${common.container}`}>
          <div
            className={`${common.corporate_golf_inner} ${common.justify_space_bet}`}
          >
            <div
              className={`${common.corporate_content_box} ${common.corporate_content_box_2}`} data-aos="fade-up" data-aos-duration="700"
            >
              {props?.AcademyData?.acf?.overview_title &&
                    <div className={`${common.title_h1}`}>
                      <h3 data-aos="fade-up" data-aos-duration="700">
                        {props?.AcademyData?.acf?.overview_title && parse(props?.AcademyData?.acf?.overview_title)}
                      </h3>
                    </div>
                  }
                  {props?.AcademyData?.acf?.overview_content &&
                    <div data-aos="fade-up" data-aos-duration="700">
                      {props?.AcademyData?.acf?.overview_content && parse(props?.AcademyData?.acf?.overview_content)}
                    </div>
                  }
            </div>
{props?.AcademyData?.acf?.overview_image &&
            <div className={`${common.corporate_slider_box} ${common.image_block_style} ${common.corporate_slider_box_2a} ${common.p_relative}`} data-aos="fade-up" data-aos-duration="700"
            >
              <div className={`${common.corporate_slider_img_h2} ${common.fill_image_wrapper}`}>
                <Image
                  src={props?.AcademyData?.acf?.overview_image?.url}
                  fill
                  style={{ objectFit: "cover" }}
                  alt="image"
                   sizes="(max-width: 768px) 100vw"
                />
                <Image
                  className={`${common.w_100} ${common.holder}`}
                  src="/images/academy_hoder.jpg"
                  width={606}
                  height={664}
                  alt="button image"
                  style={{ height: "auto", width: "100%", display: "block" }}
                />
              </div>
                  </div>
                  }
          </div>
        </div>
      </section>
      ) : null}
      
      {props &&
        props?.AcademyData &&
        props?.AcademyData?.acf &&
        (props?.AcademyData?.acf?.coaching_programs_title ||          
          props?.AcademyData?.acf?.coaching_programs) ? (
      <section
        style={{ backgroundColor: "#F5EDE6" }}
        className={`${common.d_flex} ${common.pt_100} ${common.pb_100}`}
      >
        <div className={`${common.container}`}>
           {props?.AcademyData?.acf?.coaching_programs_title &&
                <div className={` ${common.title_h1}`}>
                  <h3 data-aos="fade-up" data-aos-duration="700">
                    {props?.AcademyData?.acf?.coaching_programs_title && parse(props?.AcademyData?.acf?.coaching_programs_title)}
                  </h3>
                </div>
              }

              <ul className={`${common.colom_3_block}`}>
                {props?.AcademyData?.acf?.coaching_programs &&
                  props?.AcademyData?.acf?.coaching_programs.map((coachprogram, index) => (
                    <li data-aos="fade-up" data-aos-duration="700" className={common.card_box} key={index} >
                       {coachprogram.coaching_image &&
                      <div
                        className={` ${common.image_block_style}   ${common.p_relative}`}
                      >
                        <div className={`${common.fill_image_wrapper}`}>
                        <Image
                          src={coachprogram?.coaching_image?.url}
                          fill
                          style={{ objectFit: "cover" }}
                          alt="image"
                           sizes="(max-width: 768px) 100vw"
                        />
                        <Image
                          className={`${common.w_100} ${common.holder}`}
                          src="/images/coach_holder.jpg"
                          width={606}
                          height={664}
                          alt="button image"
                          style={{ height: "auto", width: "100%", display: "block" }}
                        />
                        </div>
                      </div>
                      }
                      
                      <div className={`${common.text_block_02} ${common.mt_30}`}>
                        <div className={`${common.min_h}`}>
                          {coachprogram.coaching_title &&
                            <div className={`${common.title_30}`}>
                              <h3>{coachprogram?.coaching_title && parse(coachprogram?.coaching_title)}</h3>
                            </div>
                          }

                          {coachprogram?.coaching_content && parse(coachprogram?.coaching_content)}
                          
                        </div>
                        {coachprogram?.datetime_and_price &&
                        <ul className={`${common.program_date_block_ul}`}>
                          {Array.isArray(coachprogram?.datetime_and_price) &&
                              coachprogram?.datetime_and_price.map((datetime, sindex) => (
                                <li key={sindex}>                                  
                            <div className={`${common.program_date_block}`}>
                              <Image
                                 src={`/images/${datetime.icon}.svg`}
                                width={20}
                                height={20}
                                alt="button image"
                              />

                              <p>
                                {datetime.title && <b>{parse(datetime.title)}</b> }
                                     {datetime.sub_text && (
                                          <>
                                            <br />
                                            {parse(datetime.sub_text)}
                                          </>
                                      )}
                              </p>
                            </div>                          
                           </li>
                              ))} 
                          </ul>
                        }
                      {coachprogram?.register_now_text &&
                        <Link
                          href="#."
                          className={`${common.btn_link} ${common.btn_link_2} ${common.mt_30}`}
                          data-aos="fade-up"
                          data-aos-duration="700"
                          onClick={() => handleOpen(index, "premium")}
                        >
                          <label>{coachprogram?.register_now_text && parse(coachprogram?.register_now_text)}</label>
                          <span>
                            <Image
                              src="/images/btn-img.svg"
                              width={30}
                              height={30}
                              alt="button image"
                            />
                          </span>
                          </Link>
                      }
                        
                      </div>
                    </li>
                  ))}

          </ul>
        </div>
      </section>
 ) : null}
    
      <section
        className={`${common.d_flex} ${common.corporate_section_padding_mobile} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
      >
        <div className={`${common.container} ${common.two_cl_main}`}>
          {props &&
        props?.AcademyData &&
        props?.AcademyData?.acf &&
            props?.AcademyData?.acf?.coaching_packages && (  
          <>
              {props?.AcademyData?.acf?.coaching_packages &&
                props?.AcademyData?.acf?.coaching_packages.map((packages, index) => (
          <div
                    className={`${common.w_100} ${common.mb_40} ${common.px_2} ${common.d_flex_wrap}`}
                    key={index}
          >
                    <ul className={`${common.two_block_ul}`}>
                      {packages?.packages_image &&
                        <li data-aos="fade-up" data-aos-duration="700">
                          <div
                            className={` ${common.image_block_style}   ${common.p_relative}`}
                          >
                            <Image
                              className={`${common.w_100}`}
                              src={packages?.packages_image?.url}
                              width={700}
                              height={602}
                              alt="button image"
                              style={{ height: "auto", width: "100%", display: "block" }}
                            />
                          </div>
                        </li>
                      }
              <li data-aos="fade-up" data-aos-duration="700">
                <div className={`${common.max_w_txt}`}>
                          {packages?.packages_title &&
                            <div className={`${common.title_h1}`}>
                              <h3 data-aos="fade-up" data-aos-duration="700">
                                {packages?.packages_title && parse(packages?.packages_title)}
                              </h3>
                            </div>
                          }
                          {packages?.packages_content &&
                            <div data-aos="fade-up" data-aos-duration="700">
                              {packages?.packages_content && parse(packages?.packages_content)}
                            </div>
                          }

                          {packages?.enquire_now_text &&
                            <Link
                              href="#."
                              className={`${common.btn_link} ${common.btn_link_2} ${common.mt_30}`}
                              data-aos="fade-up"
                              data-aos-duration="700"
                              onClick={() => handleOpen(index, "midweek")}
                            >
                              <label>{packages?.enquire_now_text && parse(packages?.enquire_now_text)}</label>
                              <span>
                                <Image
                                  src="/images/btn-img.svg"
                                  width={30}
                                  height={30}
                                  alt="button image"
                                />
                              </span>
                            </Link>
                          }
                </div>
              </li>
            </ul>
          </div>
              ))}
              
              </>
            )}
          
           {props &&
            props?.AcademyData &&
            props?.AcademyData?.acf &&
            props?.AcademyData?.acf?.golf_clinics_title && 
            props?.AcademyData?.acf?.golf_clinics_content &&  (
          <div
            className={`${common.corporate_block} ${common.mt_70}`}
            style={{ background: "#f5ede6" }}
          >
            <div className={`${common.text_block_left}`}>
                 {props?.AcademyData?.acf?.golf_clinics_title &&
                    <div className={`${common.title_h1}`}>
                      <h3 data-aos="fade-up" data-aos-duration="700">
                        {props?.AcademyData?.acf?.golf_clinics_title && parse(props?.AcademyData?.acf?.golf_clinics_title)}
                      </h3>
                    </div>
                  }
                  {props?.AcademyData?.acf?.golf_clinics_content &&
                    <div data-aos="fade-up" data-aos-duration="700">
                      {props?.AcademyData?.acf?.golf_clinics_content && parse(props?.AcademyData?.acf?.golf_clinics_content)}
                    </div>
                  }

              <ul className={`${common.corporate_ul}`}>
                <li data-aos="fade-up" data-aos-duration="700">
                  <div className={`${common.corporate_ul_block}`}>
                  {props?.AcademyData?.acf?.how_many_persons_title && 
                    <p>
                    {props.AcademyData.acf.how_many_persons_title}                     
                    </p>
                  }                      
                    <label className={`${common.corporate_select}`}>
                    {props?.AcademyData?.acf?.no_of_persons &&
                      <select onChange={handlePersonsChange}>
                         {props?.AcademyData?.acf?.no_of_persons.map((persons, pindex) => (
                           <option
                             className={common.corporate_select_opt}
                             key={pindex}
                             value={persons?.persons_number}
                           >
                             {persons?.persons_number} {route.locale == 'ar' ? "أشخاص" : "people"}
                           </option>
                         ))}
                       
                      </select>
                        }
                    </label>
                  </div>
                </li>
                <li data-aos="fade-up" data-aos-duration="700">
                  <div className={`${common.corporate_ul_block}`}>
                  {props?.AcademyData?.acf?.duration_title && 
                    <p>{props.AcademyData.acf.duration_title}</p>
                      }                     
        
                    <label className={`${common.corporate_select}`}>
                    {props?.AcademyData?.acf?.duration_lists &&
                          <select onChange={handleDurationChange}>
                          {props?.AcademyData?.acf?.duration_lists.map((duration, dindex) => (
                            <option
                              className={common.corporate_select_opt}
                              key={dindex}
                              value={duration.list_of_durations}
                            >
                              {duration.list_of_durations} {route.locale == 'ar' ? "دقيقة" : "Minutes"}
                            </option>
                          ))}                    
                          </select>
                    }
                    </label>
                    
                  </div>
                </li>

                <li data-aos="fade-up" data-aos-duration="700">
                  <div className={`${common.starting_txt}`}>
                    <ul className={`${common.starting_txt_ul}`}>
                      <li className={`${common.starting_txt_icn}`}>
                        <Image
                          src="/images/left_arrow_icn.svg"
                          width={102}
                          height={32}
                          alt="button image"
                        />
                      </li>
                      <li className={`${common.starting_txt_block}`} >

                          {calculatedPrice && calculatedPrice !== "Not Available" ? (<p>{props.AcademyData.acf.starting_from}</p>) : null}
                          <h5>
                            {calculatedPrice && calculatedPrice !== "Not Available" ? (
                              <>
                                {parse(calculatedPrice.toString())} <span>SAR</span>
                              </>
                            ) : (
                              parse(`<span className=${common.available}> ${route.locale =='ar' ? "غير متوفر" : "Not Available"}</span>`)
                            )}
                          </h5>
                        {/* <h5>{calculatedPrice && parse(calculatedPrice ? calculatedPrice : "1000" )} <span>SAR</span></h5> */}
                      </li>                      
     
                    </ul>

                  </div>
                </li>
              </ul>



                {props?.AcademyData?.acf?.golf_clinics_button_text &&
                  <Link
                    href="#."
                    className={`${common.btn_link} ${common.btn_link_2} ${common.mt_30}`}
                    data-aos="fade-up"
                    data-aos-duration="700"
                    onClick={handleOpen2}
                  >
                     <label>{props?.AcademyData?.acf?.golf_clinics_button_text &&
                      parse(props?.AcademyData?.acf?.golf_clinics_button_text)}</label>
                    <span>
                      <Image
                        src="/images/btn-img.svg"
                        width={30}
                        height={30}
                        alt="button image"
                      />
                    </span>
                  </Link>
                }  



            </div>

            <div className={`${common.text_block_right}`}>
              <Image
                src="/images/logo_icn_01.svg"
                width={314}
                height={325}
                alt="button image"
              />
            </div>
              </div>
            )}
          
        </div>
      </section>
      {props &&
        props?.AcademyData &&
        props?.AcademyData?.acf &&
        (props?.AcademyData?.acf?.instructors_title ||
          props?.AcademyData?.acf?.instructors_list) ? (
       <section
        className={`${common.d_flex} ${common.corporate_section_padding_mobile} ${common.side_line} ${common.pb_100} ${common.mobile_padding_tb}  `}
      >
        <div className={`${common.container} ${common.card_flip_container}`}>
              {props?.AcademyData?.acf?.instructors_title &&                
                  <h3 data-aos="fade-up" data-aos-duration="700">
                    {props?.AcademyData?.acf?.instructors_title && parse(props?.AcademyData?.acf?.instructors_title)}
                  </h3>                
              }
              {props?.AcademyData?.acf?.instructors_list &&
                <ul
                    // className={`${common.card_group_sec}  ${common.justify_space_bet} ${common.card_group_sec_two_cl} ${common.d_flex_wrap}`}
                    className={`${common.card_group_sec}  ${common.justify_space_bet}   ${common.d_flex_wrap} ${common.learn_card_group_sec}`}
                  >
                 {props?.AcademyData?.acf?.instructors_list &&
                      props?.AcademyData?.acf?.instructors_list.map((instructors, inindex) => (
                        <>
                  <li  className={`${common.flip_card}  `} key={inindex}>
                          <div className={common.flip_card_inner}>
                            <div className={`${common.flip_card_front}`}>
                              {instructors?.instructors_image &&
                                  <div
                                      className={`${common.image_block_style}  ${common.p_relative} ${common.mob_border0}  ${common.fill_image_wrapper}`}
                                    >
                                      <Image
                                        src={instructors?.instructors_image?.url}
                                        fill
                                        style={{ objectFit: "cover" }}
                                        alt=""
                                         sizes="(max-width: 768px) 100vw"
                                      />

                                      <Image
                                        className={`${common.w_100} ${common.holder}`}
                                        src={instructors?.instructors_image?.url}
                                        width={672}
                                        height={447}
                                        alt="button image"
                                        style={{
                                          height: "auto",
                                          width: "100%",
                                          display: "block",
                                        }}
                                      />
                                    </div>
                               
                              }
                              {instructors?.instructors_name &&
                                <div className={common.card_content}>
                                  <h5>{instructors?.instructors_name && parse(instructors?.instructors_name)}</h5>
                                </div>
                              }
                          </div>
                            <div className={`${common.flip_card_back}`}>
                              {instructors?.instructors_image &&
                               <div
                                  className={`${common.image_block_style}  ${common.p_relative} ${common.mob_d_none}  ${common.fill_image_wrapper}  `}
                                >
                                  <Image
                                    src={instructors?.instructors_image?.url}
                                    fill
                                    style={{ objectFit: "cover" }}
                                    alt="image"
                                     sizes="(max-width: 768px) 100vw"
                                  />

                                  <Image
                                    className={`${common.w_100} ${common.holder}`}
                                    src={instructors?.instructors_image?.url}
                                    width={672}
                                    height={447}
                                    alt="button image"
                                    style={{
                                      height: "auto",
                                      width: "100%",
                                      display: "block",
                                    }}
                                  />
                                </div>
                              }
                              <div className={common.card_content_back}>
                                {instructors?.instructors_name &&
                                  <h5>{instructors?.instructors_name && parse(instructors?.instructors_name)}</h5>
                                }
                                {instructors?.instructors_content &&
                                  parse(instructors?.instructors_content)
                                }
                        </div>
                      </div>
                    </div>
                  </li>
                  
                  </>
                 ))}                  
                </ul>
              }
          </div>            
        </section>
      ) : null}
    </div>
  );
};

export default becomeMember;





export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const AcademyData = await getAcademypage(locale); 
  const IRMenuData = await getThemeoptions(locale.locale);
  //console.log('data',AcademyData)
  return {
    props: {
      AcademyData: AcademyData || null,    
      IRMenuData: IRMenuData || null,
    },
    revalidate: 10,
  };
};