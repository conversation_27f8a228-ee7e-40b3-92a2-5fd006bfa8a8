@import "variable", "base";

.text_block {
    width: 80%;
    margin: auto;
    text-align: center;

    h4 {
        color: #241f21;
        text-align: center;
        // font-size: 45px;
        font-size: 2.8125rem;
        line-height: 112%;
        font-weight: 700;

        @media #{$media-950} {
            margin-bottom: 20px;
        }

    }

    p {
        color: #241f21;
        margin-top: 40px;
        text-align: center;

        font-size: 18px;
        line-height: 150%;
        font-weight: 400;

        @media #{$media-1440} {
            margin-top: 20px;
        }

        @media #{$media-1280} {
            font-size: 17px;
        }

        @media #{$media-820} {
            font-size: 16px;
            margin: 25px 0;
            text-align: start;
            width: 90%;
        }

        @media #{$media-600} {
            font-size: 15px;
            margin: 14px 0;
        }
    }

    @media #{$media-768} {
        width: 90%;
        margin-left: 5%;
        margin-right: 5%;
        position: relative;
        z-index: 1;
    }

}

.calender__table_wrapper {
    .table_title {
        background-color: #977a75;

        h3 {
            color: #ffffff;
            text-align: center;
            font-size: 1.85rem;
            line-height: 112%;
            font-weight: 400;
            margin: 0;
            padding: 15px;
        }
    }
}

.calender_list {
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #dddddd;
    margin: 0;

    >li {
        width: calc(100% / 6);
        border: 1px solid #dddddd;
        padding: 10px;
        flex-grow: 1;
        min-height: 150px;
        background-color: #ffffff;
        transition: all 0.4s ease-in-out;

        @media #{$media-1400} {
            width: calc(100% / 5);
        }

        @media #{$media-1280} {
            width: calc(100% / 4);
        }

        @media #{$media-995} {
            width: calc(100% / 3);
        }

        @media #{$media-700} {
            width: calc(100% / 2);
        }

        &:hover {
            background-color: #f0f0f0;
        }

        p {
            color: #574648;
            font-weight: 600;
        }

        .tounament_list {

            li+li {
                margin-top: 20px;
            }

            li {
                padding-inline-start: 15px;

                span {
                    color: #000000;

                    @media #{$media-500} {
                        font-size: 14px;
                        line-height: 20px;
                    }
                }

                p {
                    position: relative;
                    font-weight: 600;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 10px;
                        inset-inline-start: -10px;
                        background-color: var(--color);
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                    }
                }
            }
        }
    }
}