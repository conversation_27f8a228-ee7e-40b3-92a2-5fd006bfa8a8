import React, { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import style from "@/styles/contact.module.scss";
import ContactMap from "@/component/contactMap/contactmap";
import AOS from "aos";
import "aos/dist/aos.css";

import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getContactpage } from "@/utils/lib/server/publicServices";
import CForm from "@/component/Cform"

const Contact = (props) => {

  
  const yoastData = props?.ContactData?.yoast_head_json;

  if (!props?.ContactData) {
    return null;
  }


  return (
    <div>
      {yoastData && <Yoast meta={yoastData} />}
      
       {props &&
        props?.ContactData &&
        props?.ContactData?.acf &&
        (props?.ContactData?.acf?.banner_text || props?.ContactData?.title || props?.ContactData?.acf?.banner_locations) ? (
          <ContactMap
                Mappoints={props?.ContactData?.acf?.banner_locations}
                banner_text={parse(props?.ContactData?.acf?.banner_text || props?.ContactData?.title?.rendered)}
            />
        ) : null }
    
        <section
          className={`${common.side_line} ${common.pt_135} ${common.pb_110} `}
        >
        <div className={`${common.container}`}>
            
            <div
              className={`${common.d_flex_wrap} ${common.justify_space_bet} ${style.contact_wrap}`}
            >
        {props &&
        props?.ContactData &&
        props?.ContactData?.acf &&
        (props?.ContactData?.acf?.get_in_touch_title ||
        props?.ContactData?.acf?.get_in_touch_content || 
          props?.ContactData?.acf?.address_section) ? (  
                <div className={`${style.contact_left}`}>
                  {props?.ContactData?.acf?.get_in_touch_title &&
                    <div className={`${common.title_h1} ${common.title_45}`}>
                      <h3 data-aos="fade-up" data-aos-duration="700">
                        {parse(props?.ContactData?.acf?.get_in_touch_title)}
                      </h3>
                    </div>
                  }
                  {props?.ContactData?.acf?.get_in_touch_content &&
                    <p data-aos="fade-up" data-aos-duration="700">
                      {parse(props?.ContactData?.acf?.get_in_touch_content)}
                    </p>
                  }
                  {props?.ContactData?.acf?.address_section &&
                    <ul className={style.address_list}>
                      {props?.ContactData?.acf?.address_section &&
                        props?.ContactData?.acf?.address_section.map((contact, cgindex) => (
                          <li key={cgindex} data-aos="fade-up" data-aos-duration="700">
                            <Image
                              src={`/images/${contact.loc_image}_icon.svg`}
                              width={45}
                              height={45}
                              alt="icon"
                            />
                            <div>
                            
                             {contact.loc_image === 'call' ? (
                                  <Link href={`tel:${contact?.loc_text && parse(contact?.loc_text)}`} className="ph_number">{contact?.loc_text && parse(contact?.loc_text)}</Link>
                                ) : contact.loc_image === 'mail' ? (
                                  <Link href={`mailto:${contact?.loc_text && parse(contact?.loc_text)}`}>{contact?.loc_text && parse(contact?.loc_text)}</Link>
                                ) : (
                                  <p>
                                   {contact?.loc_text && parse(contact?.loc_text)}
                                  </p>
                                )}
                              
                            </div>
                          </li>
                        ))}
                      
                    </ul>
                  }
            </div>
            ) : null}

            <CForm crycform ={props?.ContactData?.acf} />
              
          </div>
           {props &&
        props?.ContactData &&
        props?.ContactData?.acf &&
        (props?.ContactData?.acf?.together_title ||
        props?.ContactData?.acf?.together_content || 
        props?.ContactData?.acf?.speak_up_button || 
        props?.ContactData?.acf?.speak_phone || 
          props?.ContactData?.acf?.speak_email) ? ( 
              <div className={`${style.contact_wrap} ${style.padding_220px}`}  data-aos="fade-up" data-aos-duration="700">
                {props?.ContactData?.acf?.together_title &&
                  <div className={`${common.title_h1} ${common.title_45}`}>
                    <h3 >
                      {props?.ContactData?.acf?.together_title && parse(props?.ContactData?.acf?.together_title)}
                    </h3>
                  </div>
                }
                {props?.ContactData?.acf?.together_content && parse(props?.ContactData?.acf?.together_content)}
                
                <div className={`${common.d_flex_wrap} ${style.speak_up}`}>
                  {props?.ContactData?.acf?.speak_up_button &&
                    <Link
                      href={props?.ContactData?.acf?.speak_up_button?.url}
                      target={props?.ContactData?.acf?.speak_up_button?.target}
                      className={` ${common.btn_link} ${common.btn_link}`}
                    >
                      <label>{props?.ContactData?.acf?.speak_up_button?.title && parse(props?.ContactData?.acf?.speak_up_button?.title)}</label>
                      <span>
                        <Image
                          src="/images/btn-img.svg"
                          width={30}
                          height={30}
                          alt="button image"
                        />
                      </span>
                    </Link>
                  }
                  {props?.ContactData?.acf?.speak_phone &&
                    <div className={`${common.d_flex} ${style.icon_and_text} `}>
                      <Image
                        src="/images/call_icon.svg"
                        width={45}
                        height={45}
                        alt="icon"
                      />
                      <Link href={`tel:${props?.ContactData?.acf?.speak_phone && parse(props?.ContactData?.acf?.speak_phone)}`} className="ph_number">
                        {props?.ContactData?.acf?.speak_phone && parse(props?.ContactData?.acf?.speak_phone)}</Link>
                    </div>
                  }
                  {props?.ContactData?.acf?.speak_email &&
                    <div className={`${common.d_flex} ${style.icon_and_text}`}>
                      <Image
                        src="/images/mail_icon.svg"
                        width={45}
                        height={45}
                        alt="icon"
                      />
                      <Link href={`mailto:${props?.ContactData?.acf?.speak_email && parse(props?.ContactData?.acf?.speak_email)}`}>
                        {props?.ContactData?.acf?.speak_email && parse(props?.ContactData?.acf?.speak_email)}</Link>
               
                    </div>
                  }
              </div>
          </div>
           ):null }
          </div>
        </section>      
    </div>
  );
};

export default Contact;



export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const ContactData = await getContactpage(locale);   
  // console.log('data',ContactData?.acf?.banner_locations)
  return {
    props: {
      ContactData: ContactData || null,      
    },
    revalidate: 10,
  };
};