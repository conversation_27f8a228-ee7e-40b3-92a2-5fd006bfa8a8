
@import "../../styles/variable";
@import "../../styles/base";
.section {
  padding: 105px 0px;
  background: #fff;
  text-align: center;

  @media #{$media-1366} {
    padding: 90px 0px;
  }

  @media #{$media-1280} {
    padding: 80px 0px;
  }

  @media #{$media-1024} {
    padding: 70px 0px;
  }

  @media #{$media-995} {
    padding: 60px 0px;
  }

  @media #{$media-700} {
    padding: 50px 0px;
  }

  h2{
     margin-bottom:5px;

     @media #{$media-1280} {
       font-size: 2rem;
     }

     @media #{$media-1024} {
       font-size: 1.8rem;
     }

     @media #{$media-995} {
       font-size: 1.6rem;
     }

     @media #{$media-700} {
       font-size: 1.4rem;
       margin-bottom: 8px;
     }
  }
  p{
    font-size: 15px;
    line-height: 20px;
    color: #241F21;

    @media #{$media-1280} {
      font-size: 14px;
      line-height: 18px;
    }

    @media #{$media-700} {
      font-size: 13px;
      line-height: 16px;
    }
  }
}

.subtitle {
  max-width: 1180px;
  margin: 0 auto 40px;
  font-size: 1rem;
  color: #555;

  @media #{$media-1366} {
    max-width: 1000px;
    margin: 0 auto 35px;
  }

  @media #{$media-1280} {
    max-width: 900px;
    margin: 0 auto 30px;
    font-size: 0.95rem;
  }

  @media #{$media-1024} {
    max-width: 800px;
    margin: 0 auto 25px;
    font-size: 0.9rem;
  }

  @media #{$media-995} {
    max-width: 700px;
    margin: 0 auto 20px;
    padding: 0 20px;
  }

  @media #{$media-700} {
    max-width: 100%;
    margin: 0 auto 15px;
    padding: 0 15px;
    font-size: 0.85rem;
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin: 0 auto;

  @media #{$media-1366} {
    gap: 22px;
  }

  @media #{$media-1280} {
    gap: 20px;
  }

  @media #{$media-1024} {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
  }

  @media #{$media-995} {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 0 20px;
  }

  @media #{$media-700} {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 0 15px;
  }
}

.card {
  position: relative;
  border-radius: 8px;
  cursor: pointer;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  aspect-ratio: 4/3;
  perspective: 1200px;
  perspective-origin: center center;

  @media #{$media-1366} {
    border-radius: 10px;
  }

  @media #{$media-1280} {
    border-radius: 8px;
    perspective: 1000px;
  }

  @media #{$media-1024} {
    border-radius: 6px;
    perspective: 900px;
  }

  @media #{$media-995} {
    aspect-ratio: 4/3;
    margin-bottom: 10px;
  }

  @media #{$media-700} {
    aspect-ratio: 16/9;
    border-radius: 5px;
    perspective: 800px;
  }

  &:hover {
    // box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);

    .imageContainer {
      transform: rotateY(180deg);
    }

    .titleOverlay {
      opacity: 0;
      visibility: hidden;
    }
    .back_content{
      opacity: 1;
      visibility: visible;
    }
  }

  .inner {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    overflow: hidden;

    @media #{$media-1366} {
      border-radius: 12px;
    }

    @media #{$media-1280} {
      border-radius: 10px;
    }

    @media #{$media-1024} {
      border-radius: 8px;
    }

    @media #{$media-700} {
      border-radius: 6px;
    }
  }

  .imageContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1;

    .front,
    .back {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      backface-visibility: hidden;
      border-radius: 15px;
      overflow: hidden;
      @media #{$media-700} {
        border-radius:10px;
      }
    }

    .front {
      transform: rotateY(0deg);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to left, rgb(0 0 0 / 21%), rgba(0, 0, 0, 0.09)), linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.15) 100%);
        z-index: 1;
      }

      &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 50%;
        background: linear-gradient(
          to top,
          rgba(0, 0, 0, 0.7) 0%,
          rgba(0, 0, 0, 0.3) 70%,
          rgba(0, 0, 0, 0) 100%
        );
        opacity: 0.6;
        z-index: 2;
      }
    }

    .back {
      transform: rotateY(180deg);
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: inherit;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        opacity: 0.15;
        z-index: -2;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          to left,
          rgba(255, 255, 255, 0.4),
          rgba(255, 255, 255, 0.4)
        ),
        linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
        z-index: -1;
      }
    }
  }

  .titleOverlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 35px;
    z-index: 5;
    text-align: center;
    opacity: 1;
    visibility: visible;
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    @media #{$media-1366} {
      padding: 30px;
    }

    @media #{$media-1280} {
      padding: 25px;
    }

    @media #{$media-1024} {
      padding: 20px;
    }

    @media #{$media-995} {
      padding: 15px;
    }

    @media #{$media-700} {
      padding: 15px;
    }

    h3 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      color: #fff;
      // text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
      letter-spacing: 0.5px;
      transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      @media #{$media-1366} {
        font-size: 1.4rem;
      }

      @media #{$media-1280} {
        font-size: 1.3rem;
      }

      @media #{$media-1024} {
        font-size: 1.2rem;
      }

      @media #{$media-995} {
        font-size: 1.1rem;
      }

      @media #{$media-700} {
        font-size: 1.8rem;
        letter-spacing: 0.3px;
      }
    }
  }

  .back_content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    opacity: 0;
    visibility: hidden;
    z-index: 10;
    border-radius: 15px;
    padding: 32px 24px;
    pointer-events: auto;
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    @media #{$media-1440} {
      padding: 32px 45px;
    }

    @media #{$media-1366} {
      padding: 28px 20px;
      border-radius: 12px;
    }

    @media #{$media-1280} {
      padding: 25px 18px;
      border-radius: 10px;
    }

    @media #{$media-1024} {
      padding: 22px 16px;
      border-radius: 8px;
    }

    @media #{$media-995} {
      padding: 15px 12px;
      top: 5%;
      bottom: 5%;
    }

    @media #{$media-700} {
      padding: 18px 12px;
      border-radius: 6px;
    }

    h3 {
      font-size: 26px;
      line-height: 36px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #332E30;
      letter-spacing: 0.5px;
      position: relative;
      z-index: 1;

      @media #{$media-1366} {
        font-size: 24px;
        line-height: 32px;
        margin-bottom: 14px;
      }

      @media #{$media-1280} {
        font-size: 22px;
        line-height: 30px;
        margin-bottom: 12px;
      }

      @media #{$media-1024} {
        font-size: 20px;
        line-height: 28px;
        margin-bottom: 10px;
      }

      @media #{$media-995} {
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 6px;
      }

      @media #{$media-700} {
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 6px;
        letter-spacing: 0.3px;
      }
    }

    p {
      font-size: 15px;
      line-height: 1.6;
      color: #332E30;
      margin: 0;
      max-width: 380px;
      position: relative;
      z-index: 1;
      background: rgba(217, 217, 217, 0.5);
      border-radius: 15px;
      border-style: solid;
      border-color: #ffffff;
      border-width: 1px;
      backdrop-filter: blur(2px);
      padding: 25px;

      @media #{$media-1440} {
        font-size: 14px;
        line-height: 20px;
      }

      @media #{$media-1366} {
        font-size: 14px;
        line-height: 1.5;
        max-width: 320px;
        padding: 22px;
        border-radius: 12px;
      }

      @media #{$media-1280} {
        font-size: 13px;
        line-height: 1.4;
        max-width: 280px;
        padding: 20px;
        border-radius: 10px;
      }

      @media #{$media-1024} {
        font-size: 12px;
        line-height: 1.4;
        max-width: 250px;
        padding: 18px;
        border-radius: 8px;
      }

      @media #{$media-995} {
        font-size: 11px;
        line-height: 1.3;
        max-width: 230px;
        padding: 12px;
      }

      @media #{$media-700} {
        font-size: 11px;
        line-height: 1.3;
        max-width: 100%;
        padding: 14px;
        border-radius: 6px;
      }
    }
  }
}
