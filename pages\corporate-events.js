import Banner from "@/component/banner/Banner";

import React, { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import { useRouter } from "next/router";

import "@fancyapps/ui/dist/fancybox/fancybox.css";
import { Fancybox } from "@fancyapps/ui";

import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";

// import required modules
import { Autoplay, Pagination } from "swiper/modules";

import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getEventspage } from "@/utils/lib/server/publicServices";
import FormEvents from "@/component/InquireNow";

const corporateEvents = (props) => {
  const router = useRouter();
  const yoastData = props?.EventsData?.yoast_head_json;

  if (!props?.EventsData) {
    return null;
  }
  useEffect(() => {
    Fancybox.bind("[data-fancybox='gallery']", {
      Thumbs: {
        autoStart: true,
      },
      arrows: true,
      groupAll: true,
    });

    return () => {
      Fancybox.destroy();
    };
  }, []);

  return (
    <div>
      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.EventsData &&
      props?.EventsData?.acf &&
      (props?.EventsData?.acf?.banner_image ||
        props?.EventsData?.title ||
        props?.EventsData?.acf?.banner_title) ? (
        <Banner
          backgroundImg={props?.EventsData?.acf?.banner_image?.url}
          banner_text={parse(
            props?.EventsData?.acf?.banner_title ||
              props?.EventsData?.title?.rendered
          )}
        />
      ) : null}

      {props &&
      props?.EventsData &&
      props?.EventsData?.acf &&
      (props?.EventsData?.acf?.overview_title ||
        props?.EventsData?.acf?.overview_content ||
        props?.EventsData?.acf?.gallery_images) ? (
        <section
          className={`${common.corporate_events_section} ${common.side_line} ${common.pt_100}`}
        >
          <div
            className={`${common.corporate_container} ${common.container} ${common.pb_100}`}
          >
            {props?.EventsData?.acf?.overview_title && (
              <h2 data-aos="fade-up" data-aos-duration="700">
                {parse(props?.EventsData?.acf?.overview_title)}
              </h2>
            )}
            {props?.EventsData?.acf?.overview_content && (
              <div data-aos="fade-up" data-aos-duration="700">
                {parse(props?.EventsData?.acf?.overview_content)}
              </div>
            )}
          </div>
          {props?.EventsData?.acf?.gallery_images && (
            <div className={common.overflow_hidden}>
            <div
              data-aos="fade-up"
              data-aos-duration="700"
              className={`${common.swiper_container} ${common.container} corporate_swiper`}
            >
              <Swiper
                modules={[Pagination, Autoplay]}
                className="mySwiper"
                slidesPerView={2}
                spaceBetween={10}
                speed={300}
                dir={router.locale === "ar" ? "rtl" : "ltr"}
                key={router.locale}
                autoplay={{
                  delay: 2000,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: true, // Pauses autoplay on mouse hover
                }}
                breakpoints={{
                  500: {
                    slidesPerView: 3,
                    spaceBetween: 10,
                  },
                  768: {
                    slidesPerView: 4,
                    spaceBetween: 20,
                  },
                }}
              >
                {props?.EventsData?.acf?.gallery_images &&
                  props?.EventsData?.acf?.gallery_images.map(
                    (gallery, gindex) => (
                      <SwiperSlide key={gindex}>
                        {/* <a
                                                        data-fancybox="gallery"
                                                        href={gallery?.url}
                                                        className={`${common.card_img}  `}
                                                    >
                                                    <Image
                                                        src={gallery?.url}
                                                        height={250}
                                                        width={250}
                                                        alt=""
                                                    />
                                                </a> */}
                        <a
                          data-fancybox="gallery"
                          href={gallery?.url}
                          className={`${common.image_block_style}  ${common.p_relative} ${common.shadow_none} `}
                        >
                          <Image
                            src={gallery?.url}
                            fill
                            style={{ objectFit: "cover" }}
                            alt="image"
                             sizes="(max-width: 768px) 100vw"
                          />

                          <Image
                            className={`${common.w_100} ${common.holder}`}
                            src="/images/event_placeholder.jpg"
                            width={672}
                            height={447}
                            alt="button image"
                            style={{
                              height: "auto",
                              width: "100%",
                              display: "block",
                            }}
                          />
                        </a>
                      </SwiperSlide>
                    )
                  )}
              </Swiper>
              </div>
            </div>
          )}
        </section>
      ) : null}
      {props &&
      props?.EventsData &&
      props?.EventsData?.acf &&
      (props?.EventsData?.acf?.event_inquiry_title ||
        props?.EventsData?.acf?.event_inquiry_content) ? (
        <section
          className={`${common.event_enquiry_section} ${common.pt_100} ${common.pb_100} `}
        >
          <div className={`${common.container}`}>
            <div className={`${common.event_enquiry_container} `}>
              {props?.EventsData?.acf?.event_inquiry_title && (
                <h3>{parse(props?.EventsData?.acf?.event_inquiry_title)}</h3>
              )}
              {props?.EventsData?.acf?.event_inquiry_content &&
                parse(props?.EventsData?.acf?.event_inquiry_content)}
              <FormEvents crycform={props?.EventsData?.acf} />
            </div>
          </div>
        </section>
      ) : null}
    </div>
  );
};

export default corporateEvents;

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const EventsData = await getEventspage(locale);
  //console.log('data',EventsData)
  return {
    props: {
      EventsData: EventsData || null,
    },
    revalidate: 10,
  };
};
