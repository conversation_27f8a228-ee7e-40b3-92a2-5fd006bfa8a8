import axios from "@/utils/axios";
import CONSTANTS from "@/utils/constants";
import { errorHandler } from "@/utils/utils";
import { getToken } from "@/utils/getToken";

const token = getToken(); // Get the token using the utility function

//Themeoptions

export const getThemeoptions = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.Themeoptions.ThemeoptionsApi}?lang=${locale}`,
      {
        headers,
      }
    );
    return response.data;
  } catch (error) {
    return errorHandler(error);
  }
};


// Home page
export const getHome = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.HomePage.HomeApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.HomeArPage.HomeArApi}?acf_format=standard`, {      
        headers,
      });
    }
//console.log('response', response)
    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Become a Member us page
export const getMember = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.MemberPage.MemberApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.MemberArPage.MemberArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

//  Membership us page
export const getMembership = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.MembershipPage.MembershipApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.MembershipArPage.MembershipArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};



// Privacy Policy page
export const getPrivacypage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.PrivacyPage.PrivacyApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.PrivacyArPage.PrivacyArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
// Membership Policy page
export const getmemberPolicyPage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.MenberPolicyPage.MenberPolicyApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.MenberPolicyArPage.MenberPolicyArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Terms of Service page
export const getTermspage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.TermsPage.TermsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.TermsArPage.TermsArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Cookies Settings page
export const getCookiespage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.CookiesPage.CookiesApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.CookiesArPage.CookiesArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Cookies Settings page
export const getEventspage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.EventsPage.EventsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.EventsArPage.EventsArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Experience Our Activities page
export const getExperiencepage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.ExperiencePage.ExperienceApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.ExperienceArPage.ExperienceArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
 
// Contact Us page
export const getContactpage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.ContactPage.ContactApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.ContactArPage.ContactArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
 
// Learn to Play Golf page
export const getGolfpage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.GolfPage.GolfApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.GolfArPage.GolfArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
 
// Learn to Play Academy page
export const getAcademypage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.AcademyPage.AcademyApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.AcademyArPage.AcademyArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
// Learn to Calendar page
export const getCalendarpage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.CalendarPage.CalendarApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.CalendarArPage.CalendarArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// News page
export const getNewspage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.NewsPage.NewsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.NewsArPage.NewsArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Tournament page
export const getTournamentpage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.TournamentPage.TournamentApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.TournamentArPage.TournamentArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Gallery page
export const getGallerypage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.GalleryPage.GalleryApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.GalleryArPage.GalleryArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Insights post

export const getTournamentPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.TournamentPost.TournamentPostApi}?acf_format=standard&per_page=100&_embed&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.TournamentPost.TournamentPostApi}?acf_format=standard&per_page=100&lang=ar&_embed&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Insights post

export const getInsightsPosts = async (locale, query) => {
  try {
    let response;
    const headers = getAuthHeaders();
    if (locale == "en") {
      response = await axios.get(
        `${CONSTANTS.InsightPost.InsightPostApi}?acf_format=standard&per_page=100&_embed&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.InsightPost.InsightPostApi}?acf_format=standard&per_page=100&lang=ar&_embed&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

//Insights post details

export const getInsightspostSlug = async (slug, locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.InsightsSlug.InsightsSlugApi}?acf_format=standard&per_page=100&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

//Insights post Fetch Categery name only use in home page

export const getInsightsTaxonamyName = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.Insightstaxonamy.InsightstaxonamyApi}?acf_format=standard&per_page=100&lang=${locale}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Testimonial post

export const getTestimonialPosts = async (locale, query) => {
  try {
    let response;
    const headers = getAuthHeaders();
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.TestimonialPost.TestimonialPostApi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.TestimonialPost.TestimonialPostApi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Instagram post

export const getInstaPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.InstagramPost.InstagramPostApi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.InstagramPost.InstagramPostApi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// MembershipsPost post

export const getMembershipsPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.MembershipsPost.MembershipsPostApi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.MembershipsPost.MembershipsPostApi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


//Team categories page
export const getTeamCategories = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.TeamsTaxonomy.TeamsTaxonomyApi}?acf_format=standard&lang=${locale.locale}`,
      {
        headers,
      }
    );
    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

