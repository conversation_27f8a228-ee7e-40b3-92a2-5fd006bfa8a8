import styles from './GolfTechnologySection.module.scss';
import Image from 'next/image';
import parse from "html-react-parser";
import common from "@/styles/comon.module.scss";

const GolfTechnologySection = ({ technologies,sectionTitle,sectionSubtitle  }) => {
  return (
      <section className={styles.section}>
          <div className={`${common.container}`}>
                {sectionTitle && 
                    <div className={common.title_h1}>
                        <h2>{parse(sectionTitle)}</h2>
                    </div>  
                }
                {sectionSubtitle &&
                <div className={styles.subtitle}>
                    {parse(sectionSubtitle)}
                </div>
                }
                {technologies &&
                    technologies?.length > 0 &&(
                <div className={styles.grid}>
                    {technologies.map((item, index) => (
                    <div key={index} className={styles.card}>
                        <div className={styles.inner}>
                            <div className={styles.imageContainer}>
                                <div className={styles.front}>
                                    <Image src={item?.image?.url} alt={item.title} width={400} height={250} />
                                </div>
                                <div
                                    className={styles.back}
                                    style={{
                                        backgroundImage: `url(${item?.image?.url})`
                                    }}
                                >
                                </div>
                            </div>
                            <div className={styles.titleOverlay}>
                                <h3>{item?.title && parse(item?.title)}</h3>
                            </div>
                            <div className={styles.back_content}>
                                <h3>{item?.title && parse(item?.title)}</h3>
                                <p>{item?.content && parse(item?.content)}</p>
                            </div>
                        </div>
                    </div>
                    ))}
                </div>
                )}
            </div>
        </section>
  );
};

export default GolfTechnologySection;
