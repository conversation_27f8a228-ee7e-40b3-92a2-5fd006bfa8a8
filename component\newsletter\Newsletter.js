import axios from "axios";
import { useState } from "react";
import styles from "@/styles/footer.module.scss";
import common from "@/styles/comon.module.scss";
import Image from "next/image";
import { useRouter } from "next/router";

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;
const NewsletterForm = () => {
  //console.log(props);
  // const acfOptions = props.acfOptions;
  const router = useRouter();
  const [email, setEmail] = useState("");

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
const handleSubmit = async (e) => {
    e.preventDefault();
    // Validate email before sending the request
    if (!isValidEmail(email)) {
      showMessage(
        router.locale === "ar"
          ? "يرجى إدخال بريد إلكتروني صالح."
          : "Please enter a valid email address.",
        "error"
      );
      return;
    }

    try {
      const response = await axios.post(
        `${baseURL}wp-json/newsletter/v1/subscribers`,
        { email }
      );

      if (response.data === "success") {
        setEmail(""); // Clear the email field
        showMessage(
          router.locale === "ar"
            ? "شكرًا لك! لقد تم اشتراكك بنجاح للحصول على آخر التحديثات."
            : "Thank You! You are successfully subscribed to get our latest updates.",
          "success"
        );
      }
    } catch (error) {
      handleError(error);
    }
  };

const handleError = (error) => {
    let message = "";
    if (error.response) {
      // Handle specific error status codes
      if (error.response.status === 400) {
        message =
          router.locale === "ar"
            ? "لقد قمت بالاشتراك بالفعل. يرجى المحاولة باستخدام بريد إلكتروني آخر."
            : "You have already subscribed. Please try with another email.";
      } else {
        message =
          router.locale === "ar"
            ? "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقًا."
            : "An unexpected error occurred. Please try again later.";
      }
    } else {
      message =
        router.locale === "ar"
          ? "تعذر الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت."
          : "Unable to connect to the server. Please check your internet connection.";
    }

    showMessage(message, "error");
  };

  const showMessage = (message, type) => {
    const responseElement = document.getElementById("response");
    responseElement.innerHTML = `<span class='${type} ${type === "success" ? styles.success : styles.error}'>${message}</span>`;
    setTimeout(() => {
      responseElement.innerHTML = "";
    }, 5000);
  };

  if (router.locale == "ar") {
    var placeholder = "أدخل بريدك الإلكتروني";
    var subscribe = "اشترك";
  } else {
    var placeholder = "Enter your email";
    var subscribe = "Subscribe";
  }

  return (
   
    <form className={styles.subs_form}        
          onSubmit={handleSubmit}
    >
       <div className={styles.subscribe}  data-aos="fade-up" data-aos-duration="700">
          <input
            type="email"
            placeholder= {placeholder}
            className={styles.subs_field}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            id="newsletter-email"
          />
          <button type="submit"  className={`${styles.submit_btn} ${common.btn_link} ${common.btn_link} ${styles.sub_btn} sub_btn`} >
            <label>{subscribe}</label>
                <span>
                  <Image
                    src="/images/btn-img.svg"
                    width={30}
                    height={30}
                    alt="button image"
                  />
                </span>
          </button>      
      </div>
      <span className={styles.subs_msg} id="response"></span>
    </form>
      
  );
};

export default NewsletterForm;
