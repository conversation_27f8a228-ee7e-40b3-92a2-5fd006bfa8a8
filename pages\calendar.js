import React from "react";
import Link from "next/link";
import common from "@/styles/comon.module.scss";
import style from "@/styles/calendar.module.scss";
import Banner from "@/component/banner/Banner";
import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getCalendarpage } from "@/utils/lib/server/publicServices";

const calendar = ({ CalenderData }) => { 


  if (!CalenderData) return null;

  const { yoast_head_json, acf, title } = CalenderData;

  return (
    <div>
       {yoast_head_json && <Yoast meta={yoast_head_json} />}
            
      {acf?.banner_image || title?.rendered || acf?.banner_title ? (
          <Banner
                backgroundImg={acf?.banner_image?.url}
                banner_text={parse(acf?.banner_title || title?.rendered)}
            />
      ) : null}

      {(acf?.calendar_year || acf?.calendar_title || acf?.calendar?.length > 0) && (
      <section
        className={`${common.d_flex} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
        id="calendar"
      >
            <div className={`${common.container}`}>
               {acf?.calendar_title && (
                <div className={` ${style.text_block}`}>
                  <h4 data-aos="fade-up" data-aos-duration="700">
                   {parse(acf.calendar_title)}
                  </h4>
                </div>
              )}
              {acf?.calendar?.length > 0 && (
                <div className={style.calender__table_wrapper} data-aos="fade-up" data-aos-duration="700">
                {acf?.calendar_year && (
                    <div className={style.table_title}>
                      <h3>{parse(acf.calendar_year)}</h3>
                    </div>
                  )}
                  {acf?.calendar?.length > 0 &&
                    <ul className={style.calender_list} >
                      {acf?.calendar.map((listItem, caindex) => (
                        <li key={caindex}>
                          <p>{listItem?.calendar_month && parse(listItem?.calendar_month)}</p>

                          {/* Ensure tournaments exist before rendering */}
                          {listItem?.events_list?.length > 0 && (
                            <ul className={style.tounament_list}>
                              {listItem?.events_list.map((tournament, tIndex) => (
                                <li key={tIndex} style={{ '--color': tournament?.text_color }}>
                                  {tournament?.event_date && (<span>{parse(tournament?.event_date)}</span>)}
                                   {tournament?.event_name &&(
                                  <Link href={tournament?.event_link?.url || "#."} target={tournament?.event_link?.target}>                               
                                      <p style={{ color: tournament?.text_color }}>
                                      {parse(tournament?.event_name || "")}
                                      </p>                                    
                                    </Link>
                                    )}
                                </li>
                              ))}
                            </ul>
                          )}
                        </li>
                      ))}
                    </ul>
                  }
                </div>
             )}
        </div>
        </section>
      )}
    </div>
  );
};

export default calendar;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const CalenderData = await getCalendarpage(locale);   
  // console.log('data',CalenderData)
  return {
    props: {
      CalenderData: CalenderData || null,      
    },
    revalidate: 10,
  };
};