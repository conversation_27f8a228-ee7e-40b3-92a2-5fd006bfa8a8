import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import common from "@/styles/comon.module.scss";
import style from "@/styles/footer.module.scss";
import AOS from "aos";
import "aos/dist/aos.css";
import { useRouter } from "next/router";
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import NewsletterForm from "@/component/newsletter/Newsletter";


const Footer = (props) => {
  const [footeroption, setOptions] = useState(null);
  const router = useRouter();
  useEffect(() => {
     AOS.init({
       easing: "ease-out",
       duration: 1000,
     });    
    
    
        const fetchMyAcfOptions = async (locale) => {
          try {
            const FooterPostsData = await getThemeoptions(locale);
            // console.log("header options:", locale);
            setOptions(FooterPostsData);
          } catch (error) {
            console.error("Error fetching options:", error);
          }
        };
        fetchMyAcfOptions(router.locale);
    
  }, [router]);
  
  if (!footeroption) {
    return null;
  }

  return (
    <>
      <footer className={`${style.footer} ${common.footer} ${common.pt_75} ${common.pb_40}`}>
        <div className={common.container}>
          <div className={`${common.d_flex_wrap} ${common.justify_space_bet}`}>
            <div className={style.footer_detail}  data-aos="fade-up" data-aos-duration="700">
              {footeroption?.newsletter_title && <h4>{parse(footeroption?.newsletter_title)}</h4>}
              {footeroption?.newsletter_content && <p>{parse(footeroption?.newsletter_content)}</p>}            
            </div>
            <NewsletterForm lang={router.locale} />
           
          </div>
              {footeroption?.social_media &&
                    <ul className={style.social_media}  data-aos="fade-up" data-aos-duration="700">
                      {footeroption?.social_media && footeroption?.social_media.map((social, sindex) => (
                        <li key={sindex}>
                          {social.social_icon &&
                            <Link href={social.social_link || '#.'} target="_blank">
                              <Image
                                className={`transition_opacity opacity-0`}                                
                                src={`/images/${social.social_icon}.svg`}
                                alt="social"
                                width={45}
                                height={45}
                              />
                            </Link>
                          }
                        </li>
                      ))}
                   

                    </ul>
                  }
         
          <div className={style.pattern_wrap} >
            <Image src="/images/footer_pattern.svg" fill alt="pattern"/>
          </div>
          <div className={`${style.footer_bottom} ${common.footer_bottom}`} >
            {footeroption?.copyright && <p>{parse(footeroption?.copyright)}</p>}    
            {footeroption?.footer_logo &&
              <div className={style.managed_img}>
                <Image src={footeroption?.footer_logo?.url} width={201} height={26} alt="managed"  />
              </div>
            }
            {footeroption?.footer_menu &&
              <div className={style.quick_links}>
                {footeroption?.footer_menu.map((fmenu, findex) => (
                  <Link
                    href={fmenu?.footer_menu?.url}
                    target={fmenu?.footer_menu?.target}
                    key={findex}
                  >
                    {fmenu?.footer_menu?.title && parse(fmenu?.footer_menu?.title)}</Link>
                  ))}
              
              </div>
            }
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
