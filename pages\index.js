import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Image from "next/image";
import HomeBanner from "@/component/homebanner/homeBanner";
import useResponsive from "@/component/useResponsive";
import DynamicImage from "@/component/DynamicImage";
import GolfersSay from "@/component/GolfersSay";
import common from "@/styles/comon.module.scss";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import gsap from "gsap";

// -----swipper----------
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-flip";
import "swiper/css/pagination";
import "swiper/css/navigation";
// import required modules
import { EffectFlip, Pagination, Navigation, Autoplay } from "swiper/modules";

import { useRouter } from "next/router";

// -----swipper----------

import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { parseISO, format } from "date-fns";
import { ar } from "date-fns/locale";
import {
  getHome,
  getInsightsPosts,
  getInsightsTaxonamyName,
  getTestimonialPosts,
} from "@/utils/lib/server/publicServices";

const DateConvert = (datetimeString, locale = "en") => {
  if (!datetimeString || typeof datetimeString !== "string") {
    //console.error("Invalid input for DateConvert:", datetimeString);
    return "Invalid Date";
  }

  try {
    const parsedDate = parseISO(datetimeString);
    const selectedLocale = locale === "ar" ? ar : undefined; // Use Arabic locale if selected, fallback to default
    const formattedDate = format(parsedDate, "dd MMMM yyyy", {
      locale: selectedLocale,
    });
    return formattedDate;
  } catch (error) {
    // console.error("Error parsing or formatting date:", error);
    return "Invalid Date";
  }
};

export default function Home(props) {
  const { locale } = useRouter();

  // ------------mobile-condetion-------start
  const isMobile = useResponsive();
  // console.log(isMobile);

  // ------------mobile-condetion-------end

  // ==========AOS SECTION==========

  useEffect(() => {
    AOS.init({
      easing: "ease-out",
      duration: 1000,
    });
  }, []);

  // ---------------------------hoover--image--bg-section------start
  const [backgroundImage, setBackgroundImage] = useState(
    "/images/full-slide-1.jpg"
  );
  const [previousImage, setPreviousImage] = useState(
    "/images/full-slide-1.jpg"
  );

  const [isTransitioning, setIsTransitioning] = useState(false);
  const [backgroundSetup, setBackgroundSetup] = useState(0);

  const handleMouseEnter = (bgImage, lindex) => {
    setIsTransitioning(true); // Trigger transition
    // setPreviousImage(backgroundImage);
    setPreviousImage(bgImage);
    setBackgroundSetup(lindex);
    setBackgroundImage(bgImage);

    // console.log(lindex);

    const bgMaskElement = document.getElementById("bg_mask");
    if (bgMaskElement) {
      bgMaskElement.style.opacity = "0";
    }
  };

  const handleMouseLeave = () => {
    setIsTransitioning(false); // End transition

    const bgMaskElement = document.getElementById("bg_mask");
    if (bgMaskElement) {
      bgMaskElement.style.opacity = "0.7";
    }
  };

  const yoastData = props?.homeData?.yoast_head_json;

  if (!props?.homeData) {
    return null;
  }

  useEffect(() => {
    const loader = document.querySelector(".loder");

    if (loader) {
      gsap.fromTo(
        loader,
        { opacity: 1 },
        {
          opacity: 0,
          duration: 1,
          delay: 4, // 4-second delay
          onComplete: () => {
            loader.style.display = "none";
          },
        }
      );
    }
  }, []);
  
  // ---------------------------hoover--image--bg-section------end

  return (
    <>
      {yoastData && <Yoast meta={yoastData} />}
      <div className={`${common.loder} loder`}>
        <div className={common.video_block}>
          <video
            className={common.banner_video}
            src={
              locale === "ar" ? "/images/loader_ar.mp4" : "/images/loader.mp4"
            }
            autoPlay
            loop
            muted
            playsInline
          />
        </div>
      </div>
      {props &&
        props?.homeData &&
        props?.homeData?.acf &&
        props?.homeData?.acf?.home_page_banner && (
          <HomeBanner sliders={props?.homeData?.acf?.home_page_banner} />
        )}
      <div className="mibile"></div>
      <div className="desk"></div>
      {props &&
      props?.homeData &&
      props?.homeData?.acf &&
      (props?.homeData?.acf?.about_us_title ||
        props?.homeData?.acf?.about_us_content ||
        props?.homeData?.acf?.about_us_button) ? (
        <section
          className={`${common.pt_180} ${common.pb_180}  ${common.banner_side_line} ${common.side_line} `}
        >
          <div className={`${common.container}`}>
            <div className={`${common.welcome_txt_block}`}>
              <div className={`${common.title_h1} ${common.text_center}`}>
                {props?.homeData?.acf?.about_us_title && (
                  <h2 data-aos="fade-up" data-aos-duration="700">
                    {parse(props?.homeData?.acf?.about_us_title)}
                  </h2>
                )}
                {props?.homeData?.acf?.about_us_content && (
                  <div data-aos="fade-up" data-aos-duration="700">
                    {props?.homeData?.acf?.about_us_content &&
                      parse(props?.homeData?.acf?.about_us_content)}
                  </div>
                )}
                {props?.homeData?.acf?.about_us_button?.url && (
                  <Link
                    href={props?.homeData?.acf?.about_us_button?.url}
                    target={props?.homeData?.acf?.about_us_button?.target}
                    data-aos="fade-up"
                    data-aos-duration="700"
                    className={`${common.btn_link} ${common.btn_link_2} ${common.mt_20}`}
                  >
                    <label>
                      {props?.homeData?.acf?.about_us_button?.title &&
                        parse(props?.homeData?.acf?.about_us_button?.title)}
                    </label>
                    <span>
                      <img src="/images/btn-img.svg" alt=" " />
                    </span>
                  </Link>
                )}
              </div>
            </div>
          </div>
        </section>
      ) : null}

      {props &&
      props?.homeData &&
      props?.homeData?.acf &&
      props?.homeData?.acf?.image_with_text ? (
        <>
          {isMobile ? (
            <div
              className={`${common.container} ${common.mob_padding_slider} mySwiper_mob_block`}
            >
              <Swiper
                slidesPerView={"auto"}
                key={locale}
                dir={locale === "ar" ? "rtl" : "ltr"}
                spaceBetween={20}
                freeMode={true}
                pagination={{
                  el: ".custom-pagination", // Link to external pagination container
                  clickable: true,
                  renderBullet: (index, className) => {
                    return `<span class="${className} custom-bullet">${
                      index + 1
                    }</span>`;
                  },
                }}
                autoplay={{
                  delay: 3000, // Time between slides in milliseconds
                  disableOnInteraction: false, // Prevent autoplay from stopping on user interaction
                  pauseOnMouseEnter: true, // Pause autoplay on hover
                }}
                loop={true}
                modules={[Pagination, Autoplay]}
                className="mySwiper mySwiper_mob"
              >
                {props?.homeData?.acf?.image_with_text &&
                  props?.homeData?.acf?.image_with_text.map(
                    (service, smindex) => (
                      <SwiperSlide
                        key={smindex}
                        className={`${common.service_item} ${common.service_item_mob}`}
                      >
                        {service?.abt_image && (
                          <Link
                            href={service?.abt_link?.url}
                            target={service?.abt_link?.target}
                            className={`${common.service_image}`}
                          >
                            <Image
                              src={service?.abt_image?.url}
                              alt={service?.abt_title}
                              fill
                              style={{ objectFit: "cover" }}
                              sizes="(max-width: 768px) 100vw"
                            />
                          </Link>
                        )}
                        {service?.abt_title && (
                          <div className={`${common.text}`}>
                            <Link
                              href={service?.abt_link?.url}
                              target={service?.abt_link?.target}
                            >
                              <h3>
                                {service?.abt_title &&
                                  parse(service?.abt_title)}
                              </h3>
                            </Link>
                          </div>
                        )}
                      </SwiperSlide>
                    )
                  )}
              </Swiper>

              {/* External Pagination Container */}
              <div
                className={`${common.custom_pagination} custom-pagination`}
              ></div>
            </div>
          ) : (
            <div className={common.hover_expand}>
              <ul
                className={`${common.service_ul_new}`}
                data-aos="fade-up"
                data-aos-duration="700"
              >
                {props?.homeData?.acf?.image_with_text &&
                  props?.homeData?.acf?.image_with_text.map(
                    (service, index) => (
                      <li key={index} className={`${common.service_item}`}>
                        {service?.abt_image && (
                          <Link
                            href={service?.abt_link?.url}
                            target={service?.abt_link?.target}
                            className={`${common.service_image}`}
                          >
                            <Image
                              src={service?.abt_image?.url}
                              alt={service?.abt_title}
                              fill
                              style={{ objectFit: "cover" }}
                              sizes="(max-width: 768px) 100vw"
                            />
                          </Link>
                        )}
                        {service?.abt_title && (
                          <div className={`${common.text}`}>
                            <Link
                              href={service?.abt_link?.url}
                              target={service?.abt_link?.target}
                            >
                              <h3>
                                {service?.abt_title &&
                                  parse(service?.abt_title)}
                              </h3>
                            </Link>
                          </div>
                        )}
                      </li>
                    )
                  )}
              </ul>
            </div>
          )}
        </>
      ) : null}
      {props &&
      props?.homeData &&
      props?.homeData?.acf &&
      (props?.homeData?.acf?.championship_title ||
        props?.homeData?.acf?.championship_image ||
        props?.homeData?.acf?.championship_list ||
        props?.homeData?.acf?.champ_button_1 ||
        props?.homeData?.acf?.champ_button_2) ? (
        <section
          className={`${common.word_class_section}`}
          style={{
            background: `url(${props?.homeData?.acf?.championship_image?.url}) no-repeat center/cover`,
          }}
        >
          <div
            className={`${common.container} ${common.word_class_container} `}
          >
            <div className={` ${common.text_block}`}>
              {props?.homeData?.acf?.championship_title && (
                <div
                  className={` ${common.title_h1} ${common.text_center} ${common.white_txt}`}
                >
                  <h3 data-aos="fade-up" data-aos-duration="700">
                    {props?.homeData?.acf?.championship_title &&
                      parse(props?.homeData?.acf?.championship_title)}
                  </h3>
                </div>
              )}
              <div className={`${common.count_block_wrap}`}>
                {props?.homeData?.acf?.championship_list && (
                  <ul className={` ${common.count_block} ${common.count_block_top}`}>
                    {props?.homeData?.acf?.championship_list &&
                      props?.homeData?.acf?.championship_list.map(
                        (champ, cindex) => (
                          <li
                            key={cindex}
                            data-aos="flip-right"
                            data-aos-delay="100"
                          >
                            <p>
                              {champ?.champ_title && parse(champ?.champ_title)}
                            </p>
                            <span>{champ?.champ_counter}</span>
                          </li>
                        )
                      )}
                  </ul>
                  )}
                  {props?.homeData?.acf?.championship_color_box &&
                <ul  className={` ${common.count_block}  ${common.color_block}`}>
                  {props?.homeData?.acf?.championship_color_box.map((colorListItem, coindex)=>
                    <li data-aos="flip-right" data-aos-delay="100" key={coindex}
                      style={{ '--color': colorListItem.background_color }}
                      //  className={`${colorListItem.text === 'White' ? common.black_text : ''}`}
                    >
                    <p style={{ 'color': colorListItem.text_color }}>{colorListItem.color_title}</p>
                    <span style={{ 'color': colorListItem.text_color }} >{colorListItem.color_counter}</span>
                  </li>
                    )
                  }
                </ul>
                  }
                  
              </div>
              {(props?.homeData?.acf?.champ_button_new1?.url ||
                props?.homeData?.acf?.champ_button_new2?.url) && (
                <ul className={` ${common.button_block}`}>
                  {props?.homeData?.acf?.champ_button_new1?.url && (
                    <li data-aos="fade-up" data-aos-duration="700">
                      <Link
                        href={props?.homeData?.acf?.champ_button_new1?.url}
                        target={props?.homeData?.acf?.champ_button_new1?.target}
                        className={`${common.btn_link} ${common.btn_link}`}
                      >
                        <label>
                          {props?.homeData?.acf?.champ_button_new1?.title &&
                            parse(
                              props?.homeData?.acf?.champ_button_new1?.title
                            )}
                        </label>
                        <span>
                          <Image
                            src="/images/btn-img.svg"
                            width={30}
                            height={30}
                            alt="button image"
                          />
                        </span>
                      </Link>
                    </li>
                  )}
                  {props?.homeData?.acf?.champ_button_new2?.url && (
                    <li data-aos="fade-up" data-aos-duration="700">
                      <Link
                        href={props?.homeData?.acf?.champ_button_new2?.url}
                        target={props?.homeData?.acf?.champ_button_new2?.target}
                        className={`${common.btn_link} ${common.btn_link_1}`}
                      >
                        <label>
                          {props?.homeData?.acf?.champ_button_new2?.title &&
                            parse(
                              props?.homeData?.acf?.champ_button_new2?.title
                            )}
                        </label>
                        <span>
                          <Image
                            src="/images/btn-img.svg"
                            width={30}
                            height={30}
                            alt="button image"
                          />
                        </span>
                      </Link>
                    </li>
                  )}
                </ul>
              )}
            </div>
          </div>
        </section>
      ) : null}

      {/* =====hover image section===== */}
      {props &&
      props?.homeData &&
      props?.homeData?.acf &&
      (props?.homeData?.acf?.learn_to_play_title ||
        props?.homeData?.acf?.learn_to_play_button ||
        props?.homeData?.acf?.learn_to_play_list) ? (
        <section
          className={`${common.pt_180} ${common.pb_180}  ${common.bg_hover_hm}`}
          style={{ position: "relative", overflow: "hidden" }}
        >
          {props?.homeData?.acf?.learn_to_play_list &&
            props?.homeData?.acf?.learn_to_play_list.map((item, index) => (
              <div
                className={`${common.backgroundHover} ${
                  backgroundSetup == index ? common.active : ""
                }`}
                key={index}
              >
                {" "}
                <Image
                  src={item?.learn_to_play_bg_image?.url}
                  height={800}
                  width={800}
                  alt="image"
                />
              </div>
            ))}

          <div
            className={common.container}
            style={{ position: "relative", zIndex: 50 }}
          >
            <div
              className={`${common.title_h1} ${common.pb_100} ${common.text_center}`}
            >
              {props?.homeData?.acf?.learn_to_play_title && (
                <h3 data-aos="fade-up" data-aos-duration="700">
                  {props?.homeData?.acf?.learn_to_play_title &&
                    parse(props?.homeData?.acf?.learn_to_play_title)}
                </h3>
              )}
              {props?.homeData?.acf?.learn_to_play_button?.url && (
                <Link
                  href={props?.homeData?.acf?.learn_to_play_button?.url}
                  target={props?.homeData?.acf?.learn_to_play_button?.target}
                  className={`${common.btn_link} ${common.btn_link_2} ${common.desk_btn}`}
                  data-aos="fade-up"
                  data-aos-duration="700"
                >
                  <label>
                    {props?.homeData?.acf?.learn_to_play_button?.title &&
                      parse(props?.homeData?.acf?.learn_to_play_button?.title)}
                  </label>
                  <span>
                    <Image
                      src="/images/btn-img.svg"
                      width={30}
                      height={30}
                      alt="button image"
                    />
                  </span>
                </Link>
              )}
            </div>
            {props?.homeData?.acf?.learn_to_play_list && (
              <ul
                className={`${common.section_animation_03} ${common.section_animation_03a}`}
              >
                {props?.homeData?.acf?.learn_to_play_list &&
                  props?.homeData?.acf?.learn_to_play_list.map(
                    (item, lindex) => (
                      <li
                        data-aos="fade-up"
                        data-aos-duration="700"
                        key={lindex}
                        onMouseEnter={() =>
                          handleMouseEnter(item.bgImage, lindex)
                        }
                        onMouseLeave={handleMouseLeave}
                      >
                        {item?.learnto_icon && (
                          <div className={common.play_icon}>
                            <Image
                              src={item?.learnto_icon?.url}
                              width={55}
                              height={55}
                              alt="play icon"
                            />
                          </div>
                        )}
                        <div className={common.full_slide_content}>
                          {item.learnto_title && (
                            <h3 data-aos="fade-up" data-aos-duration="700">
                              {item.learnto_title && parse(item.learnto_title)}
                            </h3>
                          )}
                          {item.learnto_content && (
                            <p data-aos="fade-up" data-aos-duration="700">
                              {item.learnto_content &&
                                parse(item.learnto_content)}
                            </p>
                          )}
                        </div>
                      </li>
                    )
                  )}
              </ul>
            )}
            <div className={common.mob_section}>
              {props?.homeData?.acf?.learn_to_play_button?.url && (
                <Link
                  href={props?.homeData?.acf?.learn_to_play_button?.url}
                  target={props?.homeData?.acf?.learn_to_play_button?.target}
                  className={`${common.btn_link} ${common.btn_link_2} ${common.mob_btn}`}
                  data-aos="fade-up"
                  data-aos-duration="700"
                >
                  <label>
                    {props?.homeData?.acf?.learn_to_play_button?.title &&
                      parse(props?.homeData?.acf?.learn_to_play_button?.title)}
                  </label>
                  <span>
                    <Image
                      src="/images/btn-img.svg"
                      width={30}
                      height={30}
                      alt="button image"
                    />
                  </span>
                </Link>
              )}
            </div>
          </div>
          <div className={`${common.bg_mask} `} id="bg_mask"></div>
        </section>
      ) : null}

      {props &&
      props?.homeData &&
      props?.homeData?.acf &&
      (props?.homeData?.acf?.private_events_title ||
        props?.homeData?.acf?.private_events_button ||
        props?.homeData?.acf?.private_events_content ||
        props?.homeData?.acf?.private_events_images) ? (
        <section
          className={`${common.d_flex} ${common.corporate_section_padding_mobile} ${common.side_line} ${common.mobile_padding_tb} ${common.h_100vh} `}
        >
          <div className={`${common.container}`}>
            <div
              className={`${common.corporate_golf_inner} ${common.justify_space_bet}`}
            >
              <div
                className={`${common.corporate_content_box} ${common.spacing_class}`}
                style={{ padding: 0 }}
              >
                {props?.homeData?.acf?.learn_to_play_title && (
                  <div className={` ${common.title_h1}`}>
                    <h3 data-aos="fade-up" data-aos-duration="700">
                      {props?.homeData?.acf?.private_events_title &&
                        parse(props?.homeData?.acf?.private_events_title)}
                    </h3>
                  </div>
                )}
                {props?.homeData?.acf?.private_events_content && (
                  <div data-aos="fade-up" data-aos-duration="700">
                    {props?.homeData?.acf?.private_events_content &&
                      parse(props?.homeData?.acf?.private_events_content)}
                  </div>
                )}
                {props?.homeData?.acf?.private_events_button?.url && (
                  <Link
                    href={props?.homeData?.acf?.private_events_button?.url}
                    target={props?.homeData?.acf?.private_events_button?.target}
                    className={`${common.btn_link} ${common.btn_link_2} ${common.mt_20}`}
                    data-aos="fade-up"
                    data-aos-duration="700"
                  >
                    <label>
                      {props?.homeData?.acf?.private_events_button?.title &&
                        parse(
                          props?.homeData?.acf?.private_events_button?.title
                        )}
                    </label>
                    <span>
                      <Image
                        src="/images/btn-img.svg"
                        width={30}
                        height={30}
                        alt="button image"
                      />
                    </span>
                  </Link>
                )}
              </div>
              {props?.homeData?.acf?.private_events_images && (
                <div className={`${common.corporate_slider_box}`}>
                  <ul
                    className={`${common.corporate_images} ${common.slider_outer} swipper_slide_block`}
                  >
                    {props?.homeData?.acf?.private_events_images &&
                      props?.homeData?.acf?.private_events_images.map(
                        (privates, pindex) => {
                          // Define the delay based on the index
                          const delay = [3000, 7000, 10000][pindex % 3]; // Cycle through delays for 3 slides

                          return (
                            <li
                              data-aos="fade-up"
                              data-aos-duration="700"
                              key={pindex}
                            >
                              <Swiper
                                effect="flip"
                                autoplay={{
                                  delay: delay, // Delay between slides
                                  disableOnInteraction: false, // Keep autoplay after interaction
                                  pauseOnMouseEnter: true, // Pause autoplay on hover
                                }}
                                speed={3000} // Transition speed
                                grabCursor={true}
                                pagination={{
                                  el: ".swiper-pagination1",
                                  clickable: true,
                                }}
                                navigation={false}
                                modules={[
                                  EffectFlip,
                                  Pagination,
                                  Navigation,
                                  Autoplay,
                                ]}
                                className="mySwiper1"
                              >
                                {Array.isArray(privates?.swiper_list) &&
                                  privates.swiper_list.map(
                                    (swiper_img, sindex) => (
                                      <SwiperSlide key={sindex}>
                                        <DynamicImage
                                          src={swiper_img?.image_slide?.url}
                                          className={"responsiveImage"}
                                        />
                                      </SwiperSlide>
                                    )
                                  )}
                              </Swiper>
                            </li>
                          );
                        }
                      )}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </section>
      ) : null}

      {props &&
      props?.homeData &&
      props?.homeData?.acf &&
      props?.homeData?.acf?.insights?.length > 0 &&
      (props?.homeData?.acf?.media_center_title ||
        props?.homeData?.acf?.media_center_link ||
        props?.homeData?.acf?.insights) ? (
        <section
          style={{ backgroundColor: "#F5EDE6" }}
          className={`${common.d_flex} ${common.pt_80} ${common.pb_80} ${common.mediacenter_hm}  ${common.overflow_mob}`}
        >
          <div className={`${common.container}`}>
            <div
              data-aos="fade-up"
              data-aos-duration="700"
              className={` ${common.title_h1}  ${common.media_title}`}
            >
              {props?.homeData?.acf?.media_center_title && (
                <h3>
                  {props?.homeData?.acf?.media_center_title &&
                    parse(props?.homeData?.acf?.media_center_title)}
                </h3>
              )}
              {props?.homeData?.acf?.media_center_link && (
                <Link
                  href={props?.homeData?.acf?.media_center_link?.url}
                  target={props?.homeData?.acf?.media_center_link?.target}
                  className={`${common.btn_read} ${common.mt_80} ${common.mt_auto}  ${common.trans}`}
                  data-aos="fade-up"
                  data-aos-duration="700"
                >
                  {props?.homeData?.acf?.media_center_link?.title &&
                    parse(props?.homeData?.acf?.media_center_link?.title)}

                  <DynamicImage src={"/images/read.svg"} />
                </Link>
              )}
              <div
                className={`  ${common.golfers_swiper_btn} ${common.media_swiper_btn}`}
              >
                <span
                  className={`${common.slider_nav} ${common.slider_prev} custom-prev1`}
                ></span>
                <span
                  className={`${common.slider_nav} ${common.slider_next} custom-next1`}
                ></span>
              </div>
            </div>

            {isMobile ? (
              <div className={` ${common.media_mob}`}>
                <Swiper
                  slidesPerView={1.15}
                  autoplay={{
                    delay: 3000, // Time between slides in milliseconds
                    disableOnInteraction: false, // Prevent autoplay from stopping on user interaction
                    pauseOnMouseEnter: true, // Pause autoplay on hover
                  }}
                  key={locale}
                  dir={locale === "ar" ? "rtl" : "ltr"}
                  spaceBetween={20}
                  freeMode={true}
                  loop={true}
                  // pagination={{
                  //   clickable: true,
                  // }}
                  navigation={{
                    prevEl: ".custom-prev1",
                    nextEl: ".custom-next1",
                  }}
                  modules={[Pagination, Autoplay, Navigation]}
                  className={`${common.mySwiper_media} mySwiper mySwiper_media_mob`}
                >
                  {props?.InsightsPostsData &&
                    props?.InsightsPostsData.map((newslist, nmindex) => (
                      <SwiperSlide key={nmindex}>
                        <div className={`${common.media_item_content}`}>
                          <div className={`${common.w_100}`}>
                            {newslist?.category_name && (
                              <span
                                className={`${common.latest_lable} ${common.mb_20}`}
                              >
                                {newslist?.category_name &&
                                  parse(newslist?.category_name[0])}
                              </span>
                            )}
                            <p>
                              {newslist?.title &&
                                parse(newslist?.title?.rendered)}
                            </p>
                            {newslist?.date && (
                              <span className={`${common.date_lable}`}>
                                {DateConvert(newslist?.date, locale)}
                              </span>
                            )}
                          </div>
                          <Link
                            href={
                              newslist.slug ? `/news/${newslist.slug}` : "#"
                            }
                            className={`${common.btn_read} ${common.mt_80} ${common.mt_auto}  ${common.trans}`}
                          >
                            {locale === "en" ? "Read more" : "اقرأ المزيد"}
                            <DynamicImage src={"/images/read.svg"} />
                          </Link>
                        </div>

                        <div className={`${common.media_item_image}`}>
                          <Link
                            href={
                              newslist.slug ? `/news/${newslist.slug}` : "#"
                            }
                          >
                            <DynamicImage
                              src={
                                newslist?._embedded?.["wp:featuredmedia"]?.[0]
                                  ?.source_url || "/images/news-placeholder.jpg"
                              }
                              className={"responsiveImage"}
                            />
                          </Link>
                        </div>
                      </SwiperSlide>
                    ))}
                </Swiper>
              </div>
            ) : (
              <div className={`${common.w_100} ${common.d_flex_wrap}`}>
                {props?.InsightsPostsData?.[0] && (
                  <div className={`${common.media_bloxk_01}`}>
                    <div
                      className={`${common.media_item_content} ${common.flex_grow_1}`}
                      data-aos="fade-up"
                      data-aos-duration="700"
                    >
                      {props?.InsightsPostsData[0] && (
                        <span
                          className={`${common.latest_lable} ${common.mb_20}`}
                        >
                          {props?.InsightsPostsData[0]?.category_name?.[0]}
                        </span>
                      )}
                      <h5>
                        {props?.InsightsPostsData[0]?.title &&
                          parse(props?.InsightsPostsData[0]?.title?.rendered)}
                      </h5>

                      <span className={`${common.date_lable}`}>
                        {DateConvert(props?.InsightsPostsData[0]?.date, locale)}
                      </span>

                      <Link
                        href={
                          props?.InsightsPostsData[0]?.slug
                            ? `/news/${props?.InsightsPostsData[0]?.slug}`
                            : "#"
                        }
                        className={`${common.btn_read} ${common.mt_80}  ${common.trans}`}
                      >
                        {locale === "en" ? "Read more" : "اقرأ المزيد"}{" "}
                        <DynamicImage src={"/images/read.svg"} />
                      </Link>
                    </div>

                    <div
                      className={`${common.media_item_image}`}
                      data-aos="fade-up"
                      data-aos-duration="700"
                    >
                      <Link
                        href={
                          props?.InsightsPostsData[0]?.slug
                            ? `/news/${props?.InsightsPostsData[0]?.slug}`
                            : "#"
                        }
                      >
                        <DynamicImage
                          src={
                            props?.InsightsPostsData[0]?._embedded?.[
                              "wp:featuredmedia"
                            ]?.[0]?.source_url || "/images/news-placeholder.jpg"
                          }
                          className={"responsiveImage"}
                        />
                      </Link>
                    </div>
                  </div>
                )}

                <div
                  className={`${common.media_bloxk_02} ${common.d_flex_wrap}`}
                >
                  <ul className={`${common.media_item_02}`}>
                    {props?.InsightsPostsData &&
                      props?.InsightsPostsData?.slice(1).map(
                        (newslist, nindex) => (
                          <li
                            data-aos="fade-up"
                            data-aos-duration="700"
                            key={nindex}
                          >
                            <div className={`${common.media_item_content}`}>
                              {newslist?.category_name && (
                                <span
                                  className={`${common.latest_lable} ${common.mb_10}`}
                                >
                                  {newslist?.category_name &&
                                    parse(newslist?.category_name[0])}
                                </span>
                              )}
                              <p>
                                {newslist?.title &&
                                  parse(newslist?.title?.rendered)}
                              </p>

                              {newslist?.date && (
                                <span className={`${common.date_lable}`}>
                                  {DateConvert(newslist?.date, locale)}
                                </span>
                              )}

                              <Link
                                href={
                                  newslist.slug ? `/news/${newslist.slug}` : "#"
                                }
                                className={`${common.btn_read} ${common.mt_10}  ${common.trans}`}
                              >
                                {locale === "en" ? "Read more" : "اقرأ المزيد"}{" "}
                                <DynamicImage src={"/images/read.svg"} />
                              </Link>
                            </div>
                          </li>
                        )
                      )}
                  </ul>
                </div>
              </div>
            )}

            {props?.homeData?.acf?.media_center_link && (
              <Link
                href={props?.homeData?.acf?.media_center_link?.url}
                target={props?.homeData?.acf?.media_center_link?.target}
                className={`${common.btn_link} ${common.btn_link_2} ${common.media_read_more_btn} ${common.mt_40}`}
                data-aos="fade-up"
                data-aos-duration="700"
              >
                <label>
                  {props?.homeData?.acf?.media_center_link?.title &&
                    parse(props?.homeData?.acf?.media_center_link?.title)}
                </label>

                <span>
                  <Image
                    src="/images/btn-img.svg"
                    width={30}
                    height={30}
                    alt="button image"
                  />
                </span>
              </Link>
            )}
          </div>
        </section>
      ) : null}
      {props &&
      props?.homeData &&
      props?.homeData?.acf &&
      props?.homeData?.acf?.testimonials?.length > 0 &&
      (props?.homeData?.acf?.testimonial_title ||
        props?.TestimonialPostData ||
        props?.homeData?.acf?.testimonials) ? (
        <GolfersSay
          sec_title={props?.homeData?.acf?.testimonial_title}
          sec_data={props?.TestimonialPostData}
        />
      ) : null}
    </>
  );
}

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const homeData = await getHome(locale);

  const query = "";
  const TestimonialPostsData = await getTestimonialPosts(locale, query);
  const InsightsPostsData = await getInsightsPosts(locale.locale, query);
  const InsightstaxonamyList = await getInsightsTaxonamyName(locale.locale); // Fetch Categery name data

  let InsightsPosts = [];
  if (homeData && homeData?.acf && Array.isArray(homeData?.acf?.insights)) {
    InsightsPosts = homeData?.acf?.insights;
  }

  // Format Investors Resources  for use in the component
  let InsightsPostsRelation = [];
  if (InsightsPosts.length > 0) {
    InsightsPostsRelation = InsightsPosts.map((id) =>
      InsightsPostsData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  // Add `catslug` to each post in InsightsPostsRelation
  InsightsPostsRelation = InsightsPostsRelation.map((post) => {
    const categorySlugs = post.categories
      ?.map((catId) => {
        const matchingCategory = InsightstaxonamyList?.find(
          (taxonomy) => taxonomy.id === catId
        );
        return matchingCategory?.name; // Return the slug if a match is found
      })
      .filter(Boolean); // Remove undefined values

    return {
      ...post,
      category_name: categorySlugs, // Add category slugs to the post
    };
  });

  /// console.log('homeData', TestimonialPostsRelation)

  let TestimonialPosts = [];
  if (homeData && homeData?.acf && Array.isArray(homeData?.acf?.testimonials)) {
    TestimonialPosts = homeData?.acf?.testimonials;
  }

  // Format Investors Resources  for use in the component
  let TestimonialPostsRelation = [];
  if (TestimonialPosts.length > 0) {
    TestimonialPostsRelation = TestimonialPosts.map((id) =>
      TestimonialPostsData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  return {
    props: {
      homeData: homeData || null,
      InsightsPostsData: InsightsPostsRelation || [],
      TestimonialPostData: TestimonialPostsRelation || [],
    },
    revalidate: 10,
  };
};
