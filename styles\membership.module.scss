@import "variable", "base";

.membership_container {
    padding: 150px 0;

    .text_block {
        width: 80%;
        margin: auto;
        text-align: center;

        h4 {
            color: #241f21;
            text-align: center;
            // font-size: 45px;
            font-size: 2.8125rem;
            line-height: 112%;
            font-weight: 700;

            @media #{$media-950} {
                margin-bottom: 20px;
            }
            @media #{$media-820} {
                text-align: start;
                width: 90%;
            }

            @media #{$media-480} {
                margin-bottom: 0px;
            }

        }

        p {
            color: #241f21;
            margin-top: 40px;
            text-align: center;

            font-size: 18px;
            line-height: 150%;
            font-weight: 400;

            @media #{$media-1440} {
                margin-top: 20px;
            }

            @media #{$media-1280} {
                font-size: 17px;
            }

            @media #{$media-820} {
                font-size: 16px;
                margin: 25px 0;
                text-align: start;
                width: 90%;
            }

            @media #{$media-600} {
                font-size: 15px;
                margin: 14px 0;
            }
        }

        @media #{$media-768} {
            width: 90%;
            margin-left: 5%;
            margin-right: 5%;
            position: relative;
            z-index: 1;
        }

    }

    @media #{$media-1440} {
        padding: 120px 0;
    }

    @media #{$media-1366} {
        padding: 100px 0;
    }

    @media #{$media-1280} {
        padding: 90px 0;
    }

    @media #{$media-1024} {
        padding: 75px 0;
    }

    @media #{$media-700} {
        padding: 50px 0;
    }

    @media #{$media-480} {
        padding: 35px 0;
    }
}

.submit_btn {
    margin-top: 40px;

    background: #805e59;
    border: 1px solid #805e59;

    &:hover {
        background: #694e4a;

        color: white;
    }
}

.section_scrollbar {
    position: relative;
    z-index: 999;
}

.btn_link_01 {
    padding-left: 50px;
    padding-right: 80px;

    @media #{$media-1366} {
        padding-left: 40px;
        padding-right: 65px;
    }

    @media #{$media-1024} {
        padding-left: 30px;
        padding-right: 55px;
    }

    @media #{$media-600} {
        padding-left: 20px;
        padding-right: 50px;
    }
}

.explore_membership_sec {
    width: 100%;
    padding: 37px 0;
    background-color: #805e59;
    position: sticky;
    top: 0;
    left: 0px;
    z-index: 10;

    .explore_membership_head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;

        h4 {
            color: #ffffff;
            text-align: center;
            // font-size: 45px;
            font-size: 2.8125rem;
            font-weight: 700;
            line-height: normal;
            margin: 0;

            @media #{$media-1440} {
                font-size: 2.6rem;
            }

            @media #{$media-950} {
                font-size: 2rem;
            }

            @media #{$media-700} {
                font-size: 3.5rem;
            }
        }

        @media #{$media-600} {
            justify-content: center;
            gap: 10px;
        }
    }

    @media #{$media-1440} {
        padding: 30px 0;
    }

    @media #{$media-1366} {
        padding: 25px 0;
    }

    @media #{$media-950} {
        padding: 15px 0;
    }
}

.explore_membership_tabs {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;

    li {
        .tab_link {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 220px;
            height: 52px;
            padding: 10px;
            color: #f5ede6;
            border: 1px solid #f5ede6;
            border-radius: 30px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border: 1px solid #f5ede61a;
            }

            &.active {
                background-color: #f5ede6;
                color: #694e4a;
            }

            @media #{$media-1440} {
                min-width: 204px;
            }

            @media #{$media-1366} {
                min-width: 180px;
            }

            @media #{$media-1024} {
                min-width: 135px;
                height: 45px;
            }

            @media #{$media-950} {
                min-width: 125px;
                height: 37px;
                font-size: 14px;
            }

            @media #{$media-480} {
                min-width: 110px;
                height: 33px;
                font-size: 13px;
            }


        }
    }
}

.card_content_bg_overlay {
    &::after {
        content: '';
        position: absolute;
        bottom: -35px;
        left: 0;
        width: 100%;
        height: 200px;
        background: linear-gradient(180deg,
                rgba(0, 0, 0, 0) 0%,
                rgba(0, 0, 0, 0.7) 100%);
                z-index: -1;
    }
}