import React from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import Banner from "@/component/banner/Banner";
import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getGolfpage, getThemeoptions } from "@/utils/lib/server/publicServices";
import { useRouter } from 'next/router';

const becomeMember = (props) => {
  const route = useRouter()
  const pathname = route.pathname
  const yoastData = props?.GolfData?.yoast_head_json;

  if (!props?.GolfData) {
    return null;
  }
// console.log('data',pathname)
  return (
    <div>

      {yoastData && <Yoast meta={yoastData} />}
      {props &&
        props?.GolfData &&
        props?.GolfData?.acf &&
        (props?.GolfData?.acf?.banner_image || props?.GolfData?.title || props?.GolfData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.GolfData?.acf?.banner_image?.url}
                banner_text={parse(props?.GolfData?.acf?.banner_title || props?.GolfData?.title?.rendered)}
            />
      ) : null}
      {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.learn_to_play_menu &&
        <section className={`${common.tab_section}`}>
          <div className={`${common.container}`}>
            <ul className={`${common.container_tab}`}>
              {props?.IRMenuData?.learn_to_play_menu &&
                props?.IRMenuData?.learn_to_play_menu.map((data, irindex) => (
                <li key={irindex}>
                  <Link                      
                      className={`${pathname == data?.learn_menu?.url ? `${common.active_tab}` : ''}`}
                      target={data?.learn_menu?.target}
                      href={data.learn_menu?.url}
                  >
                    {data?.learn_menu?.title && parse(data?.learn_menu?.title)}
                  </Link>
                </li>
              ))}  
            </ul>
          </div>
        </section>
      }
      {props &&
        props?.GolfData &&
        props?.GolfData?.acf &&
        (props?.GolfData?.acf?.overview_title ||
          props?.GolfData?.acf?.overview_content ||
          props?.GolfData?.acf?.overview_image) ? (
      <section
        className={`${common.d_flex} ${common.tab_side_line} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
      >
        <div className={`${common.container}`}>
          <div
            className={`${common.corporate_golf_inner} ${common.justify_space_bet}`}
          >
            <div
              className={`${common.corporate_content_box} ${common.corporate_content_box_2}`}
                >
                  {props?.GolfData?.acf?.overview_title &&
                     <div className={`${common.title_h1}`}>
                        <h3>{parse(props?.GolfData?.acf?.overview_title)}</h3>
                    </div>
                  }
              <div data-aos="fade-up" data-aos-duration="700">
                    {props?.GolfData?.acf?.overview_content && parse(props?.GolfData?.acf?.overview_content)}
                    {props?.GolfData?.acf?.register_now_button &&
                        <Link
                          href={props?.GolfData?.acf?.register_now_button?.url}
                          className={`${common.btn_link} ${common.btn_link_2} ${common.mt_30}`}
                          data-aos="fade-up"
                        data-aos-duration="700"
                        target={props?.GolfData?.acf?.register_now_button?.target}
                        >
                          <label>{props?.GolfData?.acf?.register_now_button && parse(props?.GolfData?.acf?.register_now_button?.title)}</label>
                          <span>
                            <Image
                              src="/images/btn-img.svg"
                              width={30}
                              height={30}
                              alt="button image"
                            />
                          </span>
                          </Link>
                      }
                <ul className={`${common.contact_list} ${common.mt_50}`}>
                      {props?.GolfData?.acf?.get_in_touch_text &&
                        <li>
                          <b>{parse(props?.GolfData?.acf?.get_in_touch_text)}</b>
                        </li>
                      }
                       
                      {props?.GolfData?.acf?.phone &&
                        <li>
                          <span className={`${common.contact_list_icn}`}>
                            <Image
                              src="/images/phone.svg"
                              width={16}
                              height={16}
                              alt="button image"
                            />
                          </span>

                          <Link href={`tel:${props?.GolfData?.acf?.phone && parse(props?.GolfData?.acf?.phone)}`} className="ph_number">
                            {props?.GolfData?.acf?.phone && parse(props?.GolfData?.acf?.phone)}
                          </Link>
                        </li>
                      }
                      {props?.GolfData?.acf?.email &&
                        <li>
                          <span className={`${common.contact_list_icn}`}>
                            <Image
                              src="/images/email.svg"
                              width={16}
                              height={16}
                              alt="button image"
                            />
                          </span>

                          <Link href={`mailto:${props?.GolfData?.acf?.email && parse(props?.GolfData?.acf?.email)}`}>
                            {props?.GolfData?.acf?.email && parse(props?.GolfData?.acf?.email)}
                          </Link>
                        </li>
                      }
                </ul>
              </div>
            </div>
                {props?.GolfData?.acf?.overview_image &&
                  <div
                    className={`${common.corporate_slider_box} ${common.border_style} ${common.image_block_style} ${common.corporate_slider_box_2a} ${common.p_relative}`}
                  >
                    <div className={`${common.corporate_slider_img_h2} ${common.fill_image_wrapper}`}>
                      <Image
                        src={props?.GolfData?.acf?.overview_image?.url}
                        fill
                        style={{ objectFit: "cover" }}
                        alt="image"
                         sizes="(max-width: 768px) 100vw"
                      />
                      <Image
                        className={`${common.w_100} ${common.holder}`}
                        src="/images/play_holder.jpg"
                        width={672}
                        height={447}
                        alt="button image"
                        style={{ height: "auto", width: "100%", display: "block" }}
                      />
                    </div>
                  </div>
                }
          </div>
        </div>
      </section>
      ) : null}
       {props &&
        props?.GolfData &&
        props?.GolfData?.acf &&
        (props?.GolfData?.acf?.driving_range_title ||          
          props?.GolfData?.acf?.driving_range) ? (
      <section
        style={{ backgroundColor: "#F5EDE6" }}
            className={`${common.d_flex} ${common.pt_100} ${common.pb_100}`}
            id="diving"
      >
        <div className={`${common.container}`}>
          {props?.GolfData?.acf?.driving_range_title &&
            <div className={` ${common.title_h1}`}>
              <h3 data-aos="fade-up" data-aos-duration="700">
                {props?.GolfData?.acf?.driving_range_title && parse(props?.GolfData?.acf?.driving_range_title)}
              </h3>
            </div>
              }
              {props?.GolfData?.acf?.driving_range &&
                <div className={` ${common.two_col_wrap}`}>
                  <ul>
                    {props?.GolfData?.acf?.driving_range &&
                      props?.GolfData?.acf?.driving_range.map((driving, drindex) => (
                        <li key={drindex}>
                          <div className={` ${common.block_03}`}>
                            {driving.driving_top_content && parse(driving.driving_top_content)}
                            {(driving.driving_price || driving.driving_price_sub_text) && (
                              <span
                                className={` ${common.amound_block_01} ${common.mb_20}`}
                              >
                                <p>
                                  {driving.driving_price && <b>{parse(driving.driving_price)}</b>}
                                  {driving.driving_price_sub_text && <span>{parse(driving.driving_price_sub_text)}</span>}
                                </p>
                              </span>
                            )}
                          
                            {driving.driving_bottom_content && parse(driving.driving_bottom_content)}
                            {driving?.driving_image &&
                              <div
                                className={` ${common.image_block_02}  ${common.p_relative} ${common.mt_50} ${common.fill_image_wrapper}`}
                              >
                                <Image
                                  src={driving?.driving_image?.url}
                                  fill
                                  style={{ objectFit: "cover" }}
                                  alt="image"
                                   sizes="(max-width: 768px) 100vw"
                                />

                                <Image
                                  className={`${common.w_100}`}
                                  src="/images/holder_02.png"
                                  width={672}
                                  height={447}
                                  alt="button image"
                                  style={{
                                    height: "auto",
                                    width: "100%",
                                    display: "block",
                                  }}
                                />
                              </div>
                            }
                          </div>
                        </li>
                      ))}
                  
                  </ul>
                </div>
              }
        </div>
      </section>
      ) : null}
      
      {/* {props &&
        props?.GolfData &&
        props?.GolfData?.acf &&
        (props?.GolfData?.acf?.instructors_title ||
          props?.GolfData?.acf?.instructors_list) ? (
       <section
        className={`${common.d_flex} ${common.corporate_section_padding_mobile} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
      >
        <div className={`${common.container} ${common.card_flip_container}`}>
              {props?.GolfData?.acf?.instructors_title &&                
                  <h3 data-aos="fade-up" data-aos-duration="700">
                    {props?.GolfData?.acf?.instructors_title && parse(props?.GolfData?.acf?.instructors_title)}
                  </h3>                
              }
              {props?.GolfData?.acf?.instructors_list &&
                <ul
                    // className={`${common.card_group_sec}  ${common.justify_space_bet} ${common.card_group_sec_two_cl} ${common.d_flex_wrap}`}
                    className={`${common.card_group_sec}  ${common.justify_space_bet}   ${common.d_flex_wrap} ${common.learn_card_group_sec}`}
                  >
                 {props?.GolfData?.acf?.instructors_list &&
                      props?.GolfData?.acf?.instructors_list.map((instructors, inindex) => (
                  <li  className={`${common.flip_card}  `} key={inindex}>
                          <div className={common.flip_card_inner}>
                            <div className={`${common.flip_card_front}`}>
                              {instructors?.instructors_image &&
                                  <div
                                      className={`${common.image_block_style}  ${common.p_relative} ${common.mob_border0}  ${common.fill_image_wrapper}`}
                                    >
                                      <Image
                                        src={instructors?.instructors_image?.url}
                                        fill
                                        style={{ objectFit: "cover" }}
                                        alt=""
                                         sizes="(max-width: 768px) 100vw"
                                      />

                                      <Image
                                        className={`${common.w_100} ${common.holder}`}
                                        src={instructors?.instructors_image?.url}
                                        width={672}
                                        height={447}
                                        alt="button image"
                                        style={{
                                          height: "auto",
                                          width: "100%",
                                          display: "block",
                                        }}
                                      />
                                    </div>
                               
                              }
                              {instructors?.instructors_name &&
                                <div className={common.card_content}>
                                  <h5>{instructors?.instructors_name && parse(instructors?.instructors_name)}</h5>
                                </div>
                              }
                          </div>
                            <div className={`${common.flip_card_back}`}>
                              {instructors?.instructors_image &&
                               <div
                                  className={`${common.image_block_style}  ${common.p_relative} ${common.mob_d_none}  ${common.fill_image_wrapper}  `}
                                >
                                  <Image
                                    src={instructors?.instructors_image?.url}
                                    fill
                                    style={{ objectFit: "cover" }}
                                    alt="image"
                                     sizes="(max-width: 768px) 100vw"
                                  />

                                  <Image
                                    className={`${common.w_100} ${common.holder}`}
                                    src={instructors?.instructors_image?.url}
                                    width={672}
                                    height={447}
                                    alt="button image"
                                    style={{
                                      height: "auto",
                                      width: "100%",
                                      display: "block",
                                    }}
                                  />
                                </div>
                              }
                              <div className={common.card_content_back}>
                                {instructors?.instructors_name &&
                                  <h5>{instructors?.instructors_name && parse(instructors?.instructors_name)}</h5>
                                }
                                {instructors?.instructors_content &&
                                  parse(instructors?.instructors_content)
                                }
                        </div>
                      </div>
                    </div>
                  </li>
                 ))}                  
                </ul>
              }
          </div>            
        </section>
      ) : null} */}
    </div>
  );
};

export default becomeMember;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const GolfData = await getGolfpage(locale); 
  const IRMenuData = await getThemeoptions(locale.locale);
  //console.log('data',GolfData)
  return {
    props: {
      GolfData: GolfData || null,    
      IRMenuData: IRMenuData || null,
    },
    revalidate: 10,
  };
};