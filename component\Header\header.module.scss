@import "../../styles/variable";
@import "../../styles/base";

.header {
    padding: 40px 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    // background: transparent;
    z-index: 55;
    // background: #3a3939;
    transition: all 0.3s ease;

    &.active {
        position: fixed !important;
    }

    @media #{$media-500} {
        padding: 30px 0;

    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        height: 100%;
        width: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.65) 0%, rgba(0, 0, 0, 0) 100%);
        z-index: -1;

    }

    &.sticky {
        position: fixed;
        top: 0;
        width: 100%;
        background-color: #805e59;
        box-shadow: 0 4px 8px rgba(0, 0, 0, .1);
        z-index: 999;
        padding: 10px 0;

        .lang_txt {
            color: #ffffff !important;

            &:hover {
                color: #dfc2bef2 !important
            }
        }
        &::after {
            display: none;
    
        }
    }

    .container {
        position: static;
    }
}

.header_inner {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    // justify-content: space-between;

    @media #{$media-1200} {
        justify-content: space-between;
    }
}

.main_logo {
    position: relative;
    width: 188px;
    height: 44px;
    display: flex;
    flex-wrap: wrap;
    z-index: 10;
    direction: ltr !important;

    // @media #{$media-1300} {
    //     transform: scale(0.7);
    // }
    @media #{$media-700} {
        width: 122px;
        height: 36px;
    }

    @media #{$media-400} {
        width: 105px;
        height: 30px;
    }
}

.logo_center {
    // width: 44px;
    width: 24%;
    overflow: hidden;
    position: relative;
}

.logo_img_1 {
    position: absolute;
    bottom: 0px;
    left: 4px;
    width: 47%;
    transform: translateY(0%);
    animation: slideUp 10s ease-in-out infinite;
}

@keyframes slideUp {

    0%,
    12% {
        transform: translateY(00%);
    }

    17% {
        transform: translateY(0%);
    }

    98%,
    100% {
        transform: translateY(0%);
    }
}

.logo_img_2 {
    position: absolute;
    top: 15px;
    left: 5px;
    width: 100%;
    transform: translateY(-170%);
    animation: slideDown 10s ease-in-out infinite;
}

@keyframes slideDown {

    5%,
    17% {
        transform: translateY(-170%);
    }

    22%,
    88% {
        transform: translateY(0%);
    }

    98%,
    100% {
        transform: translateY(-170%);
    }
}

.logo_img_3 {
    position: absolute;
    top: 0;
    left: 5px;
    width: 74%;
    transform: translateY(-115%);
    animation: slideDownTwo 10s ease-in-out infinite;
}

@keyframes slideDownTwo {

    8%,
    20% {
        transform: translateY(-115%);
    }

    25%,
    88% {
        transform: translateY(0%);
    }

    98%,
    100% {
        transform: translateY(-115%);
    }
}

.logo_right {
    width: 90px;
    margin-left: auto;
    width: 45%;
    right: 0;
    overflow: hidden;
    position: relative;
    padding-left: 5px;
}

.logo_img_text_04 {
    position: absolute;
    top: 8px;
    right: 0;
    transform: translateX(-115%);
    animation: slideRightOne 10s ease-in-out infinite;
}

@keyframes slideRightOne {

    11%,
    23% {
        transform: translateX(-115%);
    }

    28%,
    88% {
        transform: translateX(0%);
    }

    98%,
    100% {
        transform: translateX(-115%);
    }
}

.logo_img_text_05 {
    position: absolute;
    left: 0;
    top: 19px;
    transform: translateX(-115%);
    animation: slideRightTwo 10s ease-in-out infinite;
}

@keyframes slideRightTwo {

    14%,
    26% {
        transform: translateX(-115%);
    }

    31%,
    88% {
        transform: translateX(0%);
    }

    98%,
    100% {
        transform: translateX(-115%);
    }
}

.logo_left {
    // width: 53px;
    width: 28%;
    overflow: hidden;
    position: relative;
}
.logo_left>img{
    width: auto;
    height: auto;
}
.logo_img_text_01 {
    position: absolute;
    top: 0;
    transform: translateX(115%);
    animation: slideLeftOne 10s ease-in-out infinite;
}

@keyframes slideLeftOne {

    17%,
    29% {
        transform: translateX(115%);
    }

    34%,
    88% {
        transform: translateX(0%);
    }

    98%,
    100% {
        transform: translateX(115%);
    }
}

.logo_img_text_02 {
    position: absolute;
    top: 38%;
    transform: translateX(115%);
    animation: slideLeftTwo 10s ease-in-out infinite;
}

@keyframes slideLeftTwo {

    20%,
    32% {
        transform: translateX(115%);
    }

    37%,
    88% {
        transform: translateX(0%);
    }

    98%,
    100% {
        transform: translateX(115%);
    }
}

.logo_img_text_03 {
    position: absolute;
    bottom: 0;
    transform: translateX(115%);
    animation: slideLeftThree 10s ease-in-out infinite;
}

@keyframes slideLeftThree {

    23%,
    35% {
        transform: translateX(115%);
    }

    40%,
    88% {
        transform: translateX(0%);
    }

    98%,
    100% {
        transform: translateX(115%);
    }
}

.header_menu_block {
    width: calc(100% - 418px);
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;

    .header_main_menu {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        font-size: 100%;

        li {
            opacity: 1;
            padding-left: 15px;
            padding-right: 15px;
            /* Ensure all items are fully opaque initially */
            transition: opacity 0.3s ease;

            a {
                color: #fff;
                position: relative;
                font-size: 1em;

                &:hover {
                    transform: scaleX(1);
                }
            }

            @media #{$media-1366} {
                padding-left: 10px;
                padding-right: 10px;
            }

            @media #{$media-1200} {
                width: 100%;
                text-align: center;
                padding-top: 15px;
                padding-bottom: 15px;
            }

            @media #{$media-600} {
                padding-top: 13px;
                padding-bottom: 13px;
            }
        }

        @media #{$media-1300} {
            font-size: 85%;
        }

        @media #{$media-1200} {
            font-size: 80%;
        }
    }

    @media #{$media-1200} {
        display: none;
    }

    &.active {
        animation: menuDrop 0.4s linear;



        @media #{$media-1200} {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            width: 100%;
            // background: #000;
            // background: #F5EDE6;
            background: #805e59;
            left: 0;
            right: 0;
            top: 0px;
            height: 100vh;
            align-items: center;
            overflow: hidden;

            ul {
                li {
                    a {
                        font-size: 20px;

                        @media #{$media-600} {
                            font-size: 18px;
                        }
                    }
                }
            }
        }


    }




}




@keyframes menuDrop {
    from {
        opacity: 0;
        top: -150px;
        border-radius: 50px;
    }
}

/* Target other list items only when hovering over any <li> */
.header_main_menu li:hover {
    opacity: 1;
    /* Keep the hovered item fully visible */
}

.header_main_menu:hover li:not(:hover) {
    opacity: 0.3;
    /* Fade out non-hovered items only when hovering on the list */
}

.header_lang_block {
    display: flex;
    align-items: center;
    width: 230px;

    @media #{$media-1300} {
        width: 180px;
        font-size: 75%;
    }

    @media #{$media-1200} {
        width: 210px;
    }

    @media #{$media-700} {
        // width: calc(100% - 175px);
        width: fit-content;
    }
}

.hedder_buttion {
    width: 176px;
    padding-left: 40px !important;
    padding-right: 40px !important;
    font-size: 1em;

    span {
        white-space: nowrap;
    }

    @media #{$media-1280} {
        padding-left: 60px !important;
        padding-right: 60px !important;
    }

    @media #{$media-700} {
        width: 130px;

    }

    @media #{$media-600} {
        width: 110px;
    }

    @media #{$media-500} {
        width: 87px;

    }

    @media #{$media-400} {
        padding-left: 50px !important;
        padding-right: 50px !important;
    }
}

.container {
    position: static;
}

// ------------togle-menu---

$toggleSize: 40px;
$toggleMargin: 10px;
$toggleLine: 4px;
$toggleColor: #fff;

.nav_container {
    position: relative;
    display: inline-block;
    max-width: $toggleSize + $toggleMargin;
    max-height: $toggleSize + $toggleMargin;
    overflow: visible;
    outline: none;

    display: none;

    @media #{$media-1200} {
        display: block;
    }

    //&:focus-within, &:focus {
    &.is_active {
        .nav_toggle {

            &:before,
            &:after {
                box-shadow: none;
                background-color: rgb(255, 255, 255);
            }

            &:before {
                transform: rotate(-45deg);
            }

            &:after {
                transform: rotate(45deg);
            }

            @media #{$media-820} {
                margin: 0 10px;
                width: 35px;
            }
        }

        .nav_items {
            transform: translate(0, 0);
        }
    }

    .nav_toggle {
        $offset: $toggleSize * 0.5;

        position: relative;
        width: $toggleSize;
        height: $toggleSize;
        margin: $toggleMargin;
        z-index: 55;

        &:hover {
            cursor: pointer;
        }

        &:before,
        &:after {
            content: "";
            position: absolute;
            top: #{$offset - $toggleLine / 2};
            left: 0;
            transform: translate(0, 0);
            width: 100%;
            border-radius: 50px;
            height: $toggleLine;
            background: $toggleColor;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        &:before {
            box-shadow: 0 #{$offset / 1.5} 0 0 $toggleColor;
        }

        &:after {
            box-shadow: 0 #{-$offset / 1.5} 0 0 $toggleColor;
        }

        @media #{$media-820} {
            margin: 0 10px;
            width: 38px;
        }
    }

    .nav-items {
        position: absolute;
        top: 0;
        left: 0;
        min-width: 300px;
        max-width: 50vw;
        width: 100vw;
        height: 100vh;
        z-index: 55;
        padding: 80px 20px 20px 10px;
        transition: transform 0.3s ease;
        transform: translate(calc(-100% - 50px), 0);
        background: #efefef;
        display: grid;
        grid-template-columns: 1fr;
        grid-gap: 5px 0;
        align-content: start;
        box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);

        .nav-item {
            background: darken(#efefef, 5%);
            padding: 10px;
            transition: background-color 0.3s ease;

            &:hover {
                cursor: pointer;
                background: darken(#efefef, 10%);
            }
        }
    }
}