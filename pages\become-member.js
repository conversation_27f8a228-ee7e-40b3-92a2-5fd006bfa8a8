
import React from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import style from "@/styles/becomeMember.module.scss";
import Banner from "@/component/banner/Banner";


import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getMember } from "@/utils/lib/server/publicServices";
import FormMembers from "@/component/Memberform"

const becomeMember = (props) => {

  const yoastData = props?.MembersData?.yoast_head_json;

  if (!props?.MembersData) {
    return null;
  }

  return (

    <div>
        {yoastData && <Yoast meta={yoastData} />}
            
      {props &&
        props?.MembersData &&
        props?.MembersData?.acf &&
         (props?.MembersData?.acf?.banner_image || props?.MembersData?.title || props?.MembersData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.MembersData?.acf?.banner_image?.url}
                banner_text={parse(props?.MembersData?.acf?.banner_title || props?.MembersData?.title?.rendered)}
            />
        ) : null }


    <section className={`${style.form_section} ${common.side_line} ${common.pt_100} ${common.pb_100}`}>
        <div className={`${common.container} ${style.form_container} ${style.justify_space_bet}`}>
{props &&
        props?.MembersData &&
                props?.MembersData?.acf &&
            (props?.MembersData?.acf?.overview_title || props?.MembersData?.acf?.overview_content) ? (
              
          <div className={style.left_Sec} data-aos="fade-up" data-aos-duration="700"  >

           {props?.MembersData?.acf?.overview_title &&
                <h3>{parse(props?.MembersData?.acf?.overview_title)}</h3>
            }
            {props?.MembersData?.acf?.overview_content &&
                parse(props?.MembersData?.acf?.overview_content)
            }
                

          </div>
          ) : null }

           <div className={style.right_form_Sec}>
           <FormMembers crycform={props?.MembersData?.acf} />
          </div>


        </div>

      </section>
    </div>
  );
};

export default becomeMember;



export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const MembersData = await getMember(locale);   
  //console.log('data',MembersData)
  return {
    props: {
      MembersData: MembersData || null,      
    },
    revalidate: 10,
  };
};