@import '../../styles/variable';
@import '../../styles/base';

.banner_main {
    height: 100vh;
    position: relative;

    .banner_swiper_navigation {
        top: 50%;
        transform: translate(-50%, -50%);
        left: 50%;
        position: absolute;
        max-width: var(--container-width);
        width: 100%;
        z-index: 2;


        .hm_swipper_arrow {
            position: absolute;

            z-index: 2;

            cursor: pointer;

            &:hover {
                &:after {
                    background: rgba(0, 0, 0, 1);
                    ;
                }
            }

            &:before {
                content: "";
                position: absolute;
                width: 50px;
                height: 50px;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 100;
                background-position: center !important;
                opacity: 1;
                background-size: 170% !important;
                z-index: 3;

                @media #{$media-820} {
                    width: 50px;
                    height: 50px;
                    background-size: 130% !important;


                }
            }

            &:after {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-41deg);
                width: 70px;
                height: 70px;
                background: rgba(255, 255, 255, 20%);
                backdrop-filter: blur(11.699999809265137px);
                transition: var(--transition);
                z-index: 2;
                @media #{$media-1600} {
                    width: 55px;
                    height: 55px;
                }


                @media #{$media-820} {
                    width: 50px;
                    height: 50px;
                    background-size: 130% !important;


                }
            }

            &.hm_banner_prev {
                &:before {
                    background: url(/images/arrow-left-1.png) no-repeat;
                }

                &:hover {
                    &:before {
                        transform: translate(-63%, -50%);
                        transition: all .4s ease-out 0s;
                        -moz-transition: all .4s ease-out 0s;
                        -webkit-transition: all .4s ease-out 0s;
                        -o-transition: all .4s ease-out 0s;
                    }
                }
            }

            &.hm_banner_next {
                right: 7%;
                @media #{$media-1600} {
                    right: 5%;
                }

                &:before {
                    background: url(/images/arrow-right-1.png) no-repeat;
                }

                &:hover {
                    &:before {
                        transform: translate(-30%, -50%);
                        transition: all .4s ease-out 0s;
                        -moz-transition: all .4s ease-out 0s;
                        -webkit-transition: all .4s ease-out 0s;
                        -o-transition: all .4s ease-out 0s;
                    }
                }

            }


            &.hm_banner_prev {
                left: 7%;
                @media #{$media-1600} {
                    left: 5%;
                }
            }




        }





        @media #{$media-700} {
            display: none;
        }
    }

    .main_slider {
        height: 100vh;
    }

    .banner_video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
    }

    .banner_txt {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        padding-bottom: 150px;
        // bottom: -100px;
        bottom: 0px;
        transition: all .4s ease-out 0s;
        -moz-transition: all .4s ease-out 0s;
        -webkit-transition: all .4s ease-out 0s;
        -o-transition: all .4s ease-out 0s;
        z-index: 800;

        h1 {
            color: #ffffff;
            font-size: 4.0625rem;
            line-height: 121%;
            font-weight: 700;
            text-align: center;

            @media #{$media-600} {
                padding: 0 4%;
            }

        }
    }

    .banner_button_blok {
        width: 100%;
        text-align: center;
        margin-top: 35px;

        ul li a {
            background: #F5EDE6;
            color: #000;
            border: none;

            span {
                background: #805e59;
            }
        }

    }
}

//  background: url(../images/dot-bg.png) no-repeat center center !important; 

.swiper-pagination-bullet-active {
    overflow: hidden;
    width: 60px !important;
    height: 40px !important;
    background-size: cover !important;
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    text-indent: -1000px !important;
    opacity: 1 !important;
}

.swiper-pagination-bullet:not(.swiper-pagination-bullet-active) {
    text-indent: -200px;
    overflow: hidden;
}

.swiper-pagination-bullet {
    width: 12px !important;
    height: 12px !important;
    opacity: 0.5 !important;
    margin-left: 10px !important;
    margin-right: 10px !important;
}

.swiper-pagination {
    padding-bottom: 25px;
}