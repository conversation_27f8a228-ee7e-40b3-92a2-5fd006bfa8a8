@font-face {
	font-family: 'Adelle Sans';
	src: url('../public/fonts/AdelleSans-Bold.woff2') format('woff2'),
		url('../public/fonts/AdelleSans-Bold.woff') format('woff');
	font-weight: bold;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans';
	src: url('../public/fonts/AdelleSans-Regular.woff2') format('woff2'),
		url('../public/fonts/AdelleSans-Regular.woff') format('woff');
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans';
	src: url('../public/fonts/AdelleSans-Light.woff2') format('woff2'),
		url('../public/fonts/AdelleSans-Light.woff') format('woff');
	font-weight: 300;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: '<PERSON>elle Sans';
	src: url('../public/fonts/AdelleSans-Thin.woff2') format('woff2'),
		url('../public/fonts/AdelleSans-Thin.woff') format('woff');
	font-weight: 100;
	font-style: normal;
	font-display: swap;
}



@font-face {
	font-family: 'Kufam';
	src: url('../public/fonts/Kufam-VariableFont_wght.woff') format('woff');
	font-weight: 100;
	font-style: normal;
	font-display: swap;
}





@font-face {
	font-family: 'Roboto';
	src: url('../public/fonts/Roboto-Regular.woff2') format('woff2'),
		url('../public/fonts/Roboto-Regular.woff') format('woff');
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'AktivGrotesk';
	src: url('../public/fonts/AktivGrotesk_W_Rg.woff2') format('woff2'),
		url('../public/fonts/AktivGrotesk_W_Rg.woff') format('woff');
	font-weight: 400;
	font-style: normal;
}

/*------------------ arabic font----------------- */

@font-face {
	font-family: 'Adelle Sans Arabic';
	src: url('../public/fonts/arabic/AdelleSansMultiscript-Light.woff2') format('woff2'),
		url('../public/fonts/arabic/AdelleSansMultiscript-Light.woff') format('woff');
	font-weight: 300;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans Arabic';
	src: url('../public/fonts/arabic/AdelleSansMultiscript-Heavy.woff2') format('woff2'),
		url('../public/fonts/arabic/AdelleSansMultiscript-Heavy.woff') format('woff');
	font-weight: 900;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans Arabic';
	src: url('../public/fonts/arabic/AdelleSansMultiscript-Thin.woff2') format('woff2'),
		url('../public/fonts/arabic/AdelleSansMultiscript-Thin.woff') format('woff');
	font-weight: 100;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans Arabic';
	src: url('../public/fonts/arabic/AdelleSansMultiscript-Extrabold.woff2') format('woff2'),
		url('../public/fonts/arabic/AdelleSansMultiscript-Extrabold.woff') format('woff');
	font-weight: bold;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans Arabic';
	src: url('../public/fonts/arabic/AdelleSansMultiscript-Bold.woff2') format('woff2'),
		url('../public/fonts/arabic/AdelleSansMultiscript-Bold.woff') format('woff');
	font-weight: bold;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans Arabic';
	src: url('../public/fonts/arabic/AdelleSansMultiscript-Regular.woff2') format('woff2'),
		url('../public/fonts/arabic/AdelleSansMultiscript-Regular.woff') format('woff');
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Adelle Sans Arabic';
	src: url('../public/fonts/arabic/AdelleSansMultiscript-Semibold.woff2') format('woff2'),
		url('../public/fonts/arabic/AdelleSansMultiscript-Semibold.woff') format('woff');
	font-weight: 600;
	font-style: normal;
	font-display: swap;
}


::-webkit-scrollbar {
	width: 7px;
	border-radius: 10px;
	background: rgba(255, 255, 255, 1);
}

::-webkit-scrollbar-track {
	border-radius: 10px;
	background: rgba(255, 255, 255, 1);

}

::-webkit-scrollbar-thumb {
	background: #212121;
	border-radius: 10px;
	width: 7px;
}

::-webkit-scrollbar-thumb:hover {
	background: #111;
}

:root {
	--black: #000000;
	--primary: #088A9A;
	--primary1: #29C6DA;
	--secondary: #000000;
	--white: #fff;
	--font-primary: 'Adelle Sans';
	--font-primary-ar: 'Adelle Sans Arabic';
	--transition: all 0.3s ease-in-out;
}

body {
	background-color: #fff;
	font-size: 16px;
	line-height: 27px;
	font-family: var(--font-primary), var(--font-primary-ar);
	font-weight: normal;
	overflow-x: hidden;
	/* --container-width: 1630px; */
}

* h1,
h2,
h3,
h4,
h5,
h6,
p,
ul {
	margin: 0;
	padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: var(--font-primary), var(--font-primary-ar);
	font-weight: 500;
	color: var(--secondary);
	margin-bottom: 25px;
}

h1,
.heading-1 {
	font-size: 50px;
	line-height: 56px;
}

h2,
.heading-2 {
	font-size: 44px;
	line-height: 44px;
}

h3,
.heading-3 {
	font-size: 40px;
	line-height: 48px;
}

h4,
.heading-4 {
	font-size: 23px;
	line-height: 28px;
	font-weight: 700;
}

h5,
.heading-5 {
	font-size: 22px;
	line-height: normal;
}

h6,
.heading-6 {
	font-size: 20px;
	line-height: 22px;
}

* {
	box-sizing: border-box;
	margin: 0px;
	padding: 0px;
}

p {
	font-size: 16px;
	line-height: 27px;
	font-family: var(--font-primary), var(--font-primary-ar);
	font-weight: 400;
	color: #000000;
	margin-bottom: 20px;



}

/* p:last-child {
	margin-bottom: 0px;
} */

ul {
	margin-bottom: 0px;
}

ul li {
	list-style: none;
}

a,
button {
	font-size: 1rem;
	text-decoration: none;
	font-family: var(--font-primary), var(--font-primary-ar);
	font-weight: 500;
	line-height: 1;
	color: var(--black);
	transition: var(--transition);
}

a:hover {
	text-decoration: none !important;
	color: #1A1C1E;
}

input,
textarea,
select {
	background: none;
	outline: none;
	font-size: 1rem;
	color: var(--black);
	border: 1px solid;
	font-family: var(--font-primary), var(--font-primary-ar);
}

img {
	outline: none;
	max-width: 100%;
	height: auto;
	border: 0px none;
}

input[type='submit'] {
	-webkit-appearance: none;
	appearance: none;
	outline: none;
}

:focus {
	outline: none;
}
::selection {
	color: #ffffff;
	background: #805e59;
  }

.container {
	width: 100%;
	max-width: calc(1630px + 6%);
	padding-left: 3%;
	padding-right: 3%;
	margin: 0 auto;


}

@media screen and (max-width:1600px) {
	/* body{
    --container-width: 1220px;
  } */
	/* .container {
		padding-left: 6%;
		padding-right: 6%;

	} */
}

.responsiveImage {
	width: 100%;
	height: 100%;
	object-fit: cover;
	display: block;
}

.responsiveImage2 {
	max-width: 100%;
	height: auto;
}

.swipper_slide_block .swiper-slide {
	border-radius: 20px;
	overflow: hidden;
	box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.301);
}





.mySwiper_mob .swiper {
	overflow: visible !important;
}


.mySwiper_mob .swiper-pagination {
	bottom: -20px;
}

.mySwiper_mob_block .custom-bullet {
	text-indent: -100px;
	overflow: hidden;
	margin: 5px;
	width: 8px;
	height: 8px;
}




.mySwiper_mob_block .custom-bullet.swiper-pagination-bullet-active {
	background: #805E59;
}


.mySwiper_mob_block .swiper-slide {
	flex-direction: column;
	height: unset;
	display: flex;
}


.mySwiper_media_mob .swiper-slide {
	height: unset !important;
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	border-radius: 15px !important;
	overflow: hidden;
	border: 1px solid rgb(255, 255, 255);
	background-color: rgba(255, 255, 255, 0.4);
	;
}

.mob_what_section .swiper {
	overflow: visible;
}

.swipper_what_our {
	overflow: visible;
}

.swipper_what_our .swiper-slide {
	width: 85%;
}


.css-1r2bn3-JoySvgIcon-root {
	fill: white !important;
}

/* overflow: hidden;
width: 60px !important;
height: 40px !important;
background: url(../images/dot-bg.png) no-repeat center center !important;
background-size: cover !important;
display: inline-flex !important
;
align-items: center;
justify-content: center;
text-indent: -1000px !important;
opacity: 1 !important; */
.banner_main>.swiper-horizontal>.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet-active {
	overflow: hidden;
	width: 60px !important;
	height: 40px !important;
	margin: 0 15px !important;
	background: url('/images/swiper-pagination-icon.svg') no-repeat center center !important;
	background-size: contain !important;
	opacity: 1 !important;

}

@media screen and (max-width:1600px) {
	.banner_main>.swiper-horizontal>.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet-active {
		width: 50px !important;
		height: 35px !important;
	}
}

.corporate_swiper>.swiper {
	overflow: visible;
}

.banner_main>.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal>.swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	bottom: 30px !important;
}

.banner_main>.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
	width: 13px !important;
	height: 13px !important;
	opacity: 0.6 !important;

}

@media screen and (max-width:500px) {
	.banner_main>.swiper-horizontal>.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet-active {
		width: 45px !important;
		margin: 0 10px !important;

	}

	.banner_main>.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,
	.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
		width: 10px !important;
		height: 10px !important;

	}

	.mySwiper_mob_block .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
		background-color: rgba(128, 94, 89, 0.4);
		margin-left: 7.5px;
		margin-right: 7.5px;
		width: 8px;
		height: 8px;
	}

	.mySwiper_mob_block .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active {
		background-color: rgba(128, 94, 89, 1);
	}

	.mySwiper_mob_block {
		overflow: hidden;
	}

	.mySwiper_mob_block .swiper {
		overflow: visible;
	}

	.mySwiper_mob_block .swiper-slide {
		box-shadow: 20px 13px 31px 0 rgba(87, 70, 72, .2);
	}
}

@media screen and (max-width:768px) {

	.css-1r2bn3-JoySvgIcon-root {

		height: 20px !important;
		width: 20px !important;
	}

	.mySwiper_media_mob.swiper {
		overflow: visible;
	}
}

@media screen and (max-width:500px) {

	.css-ylro10-JoySheet-root {

		width: 95% !important;
	}

	p {
		font-size: 14px;
		line-height: 20px;

	}
}



@media screen and (max-width:1500px) {


	#font_size {
		font-size: 85%;
	}

}

@media screen and (max-width:1280px) {


	#font_size {
		font-size: 80%;
	}

	a {
		font-size: 15px;
	}

}

@media screen and (max-width:820px) {


	#font_size {
		font-size: 70%;
	}

}

.form-error,
.error,
.msg_error {
	color: #d75252;
	font-size: 14px;
}

.success,
.form-success,
.msg_success {
	color: #43832a;
	font-size: 14px;
}


@media screen and (max-width:700px) {
	#font_size {
		font-size: 50%;
	}

	a {
		font-size: 14px;
	}

	button {
		font-size: 14px;
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		margin-bottom: 10px;
	}


	input,
	textarea,
	select {
		font-size: 14px;
	}
}

hr {

	border: none;
	/* Remove the default border */
	border-top: 2px solid #fff;
	/* Set your desired color and thickness */
}


.mySwiper_media {}

/* .rtl body {
	font-family: var(--font-primary-ar);
}

.rtl h1,
.rtl h2,
.rtl h3,
.rtl h4,
.rtl h5,
.rtl p,
.rtl a,
.rtl span,
.rtl label {
	font-family: var(--font-primary-ar);
 
} */
@media screen and (max-width:500px) {

	input,
	textarea,
	select {
		font-size: 16px;
	}
}

/* .rtl p{
	font-size: 14px !important;
    line-height: 26px;
}
@media screen and (min-width:1501px) {
html[dir="rtl"]#font_size {
	font-size: 85%;
}
} */
.gm-style-iw-d {
	max-height: unset !important;
	overflow: auto !important;
	padding-bottom: 15px;
	padding-inline-end: 15px;
}

.gm-style-iw-d h3 {
	font-size: 1.8rem;
	font-weight: 400;
	margin-bottom: 10px;
}

.gm-style-iw-d p {
	font-size: 14px;
}

.gm-style-iw-d a {
	font-size: 15px;
	color: #574547;
	text-decoration: underline;
}

/* .rtl .header_menu_block .header_main_menu{
font-size: 85%;
}
.rtl .header_lang_block{
	font-size: 85%;
} */
.ph_number {
	direction: ltr;
}

.related_news_swipper .swiper-wrapper {
	align-items: stretch;

}

.related_news_swipper .swiper-wrapper .swiper-slide {
	height: auto;
}

.react-tel-input .selected-flag,
.react-tel-input .flag-dropdown {
	border: 0 !important;
	background: transparent !important;
}

.react-tel-input .selected-flag {
	padding: 0 0 0 20px !important;
	width: 65px !important;
}
.rtl .react-tel-input .selected-flag{
	padding:  0 20px 0 0 !important;
  
}
.rtl .react-tel-input .dial-code{
	margin-right: 5px;
}

.ph_feild_new .react-tel-input .selected-flag .arrow {
	width: 8px;
	height: 8px;
	border-left: 1px solid transparent !important;
	border-right:1px solid black !important;
	border-top: 1px solid transparent !important;
	border-bottom: 1px solid black !important;
	transform: translateY(-50%) rotate(45deg);
	inset-inline-start: 30px;
}
.react-tel-input .selected-flag .arrow.up{
	transform: rotate(-135deg);
}


.ph_feild_new .react-tel-input {
	font-family: var(--font-primary), var(--font-primary-ar);
}