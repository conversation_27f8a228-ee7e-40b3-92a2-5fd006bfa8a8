import React from "react";
import common from "@/styles/comon.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";

const NotFound = () => {
const router = useRouter()
  return (
    <div className={common.not_found_sec}>
      <div className={`${common.container} ${common.not_found}`}>
        <h1>404</h1>
        <h4>{router.locale == 'ar' ? 'لم يتم العثور على هذه الصفحة.':'This page could not be found.'}</h4>
        <Link
          href={"/"}
          data-aos="fade-up"
          data-aos-duration="700"
           className={`${common.btn_link} ${common.btn_link_2}  ${common.card_button} ${common.mt_20}`}
        >
          <label>{router.locale == 'ar' ? 'العودة الى الصفحة الرئيسية': 'Back to Home'}</label>
          <span>
            <img src="/images/btn-img.svg" alt=" " />
          </span>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
