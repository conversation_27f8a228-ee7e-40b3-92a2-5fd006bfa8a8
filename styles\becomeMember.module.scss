@import "variable", "base";

.form_section {
    width: 100%;
    

    .form_container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;




    }
}

.left_Sec {
    width: 30%;
  

    h3 {
        color: #241f21;
 
        font-size: 2.813rem;
        line-height: 120%;
        font-weight: 700;
    }

    p {
        color: #241f21;
       
        font-size: 16px;
        line-height: 150%;
        font-weight: 400;
        @media #{$media-820} { 
            font-size: 14px;
        }
    }



    @media #{$media-700} {

        width: 100%; padding-right: 0;
    }
}

.right_form_Sec {
    width: 60%;

    .right_form_ul {
        width: 100%;

        li {
            width: 100%;
            margin-bottom: 15px;

            input,
            select,
            textarea {
                border-radius: 30px;
                font-size: 1rem;
                padding: 13px 30px;
                width: 100%;
                border: 1px solid #d3d3d3;
                color: #2E2E2E;
                height: 50px;
                @media #{$media-700} {
                    font-size: 14px;
                }
                @media #{$media-500} {
                    font-size: 16px;
                }
                &::placeholder{
                    color: #2E2E2E;
                }


                &.textarea_field {
                    border-radius: 20px;
                    max-height: 250px;
                    min-height: 200px;
                    min-width: 100%;
                    max-width: 100%;
                    resize: none;
                }




            }

            // &.select_field {
            //     position: relative;

            //     select {
            //         height: 100%;
            //         appearance: none;
            //         -webkit-appearance: none;
            //         -moz-appearance: none;


            //     }

            //     &::after {
            //         content: '';
            //         position: absolute;
            //         height: 20px;
            //         width: 20px;
            //         top: 50%;
            //         transform: translateY(-50%);
            //         right: 10px;
            //         /* Adjust based on your design */
            //         background-image: url('/image/dropdown-arrow.png');
            //         background-repeat: no-repeat;
            //         background-size: contain;
            //         pointer-events: none;
            //     }
            // }
        }
    }

    @media #{$media-700} { width: 100%; margin-top: 25px;}

 }

// .submit_btn {

//     background: #805e59;
//     border-radius: 30px;
//     padding-top: 5px;
//     padding-bottom: 5px;
//     padding-inline-start: 35px;
//     padding-inline-end: 7px;
//     color: #f5ede6;
//     text-align: center;
//     font-size: 1rem;
//     line-height: 28px;
//     font-weight: 400;
//     border: 0;
//     height: 48px;
//     display: flex;
//     align-items: center;
//     justify-content: flex-end;
//     // padding: 7px;
//     gap: 13px;
//     cursor: pointer;
//     transition: all 0.3s ease;
//     @media #{$media-1440} {
//         height: 40px;
//         padding-inline-end: 2px;
//     }
//     @media #{$media-1280} {
//         font-size: 14px;
//     }


//     &:hover {
//         background: #684c48;

//     }



// }