import React, { useEffect,  useState, useRef} from "react";
import Footer from "@/component/footer/Footer";
import Header from "@/component/Header/header";
import comon from "@/styles/comon.module.scss";
import AOS from "aos";
import "aos/dist/aos.css";
// -------- Font Implementation End --------

const Layout = (props) => {


  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile

  const [isTb, setIsTab] = useState(false);





  // Use effect to initialize Locomotive Scroll
  useEffect(() => {
    (async () => {
      const LocomotiveScroll = (await import("locomotive-scroll")).default;
      const locomotiveScroll = new LocomotiveScroll({
        el: document.querySelector("#main-element"), // Target element for smooth scrolling
        smooth: true, // Enables smooth scrolling
        lerp: 25, // Adjust the interpolation value for smoother motion (default is usually 0.1)
        smoothMobile: true, // Enable smooth scrolling for mobile devices
        multiplier: 1.2, // Adjust the scroll speed multiplier (higher is faster)
        touchMultiplier: 2, // Speed multiplier for touch devices
        inertia: 0.8, // Set the inertia for a smoother deceleration effect
      });

      return () => {
        // Clean up LocomotiveScroll instance when the component unmounts
        locomotiveScroll.destroy();
      };
    })();
  }, []);


  useEffect(() => {
   

    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
      setIsTab(window.innerWidth <= 1000);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const scrollRef = useRef(0); // To track the scroll count
  const throttleTimeout = useRef(null);

  useEffect(() => {
    // Initialize AOS on component mount
    AOS.init({
      easing: "ease-out-cubic",
      once: false,
      offset: 50,
    });

    const handleScrollOrResize = () => {
      if (!throttleTimeout.current) {
        throttleTimeout.current = setTimeout(() => {
          throttleTimeout.current = null;

          // Increment the scroll counter
          if (scrollRef.current <= 10) {
            scrollRef.current += 1;
          } else {
            // Refresh AOS and reset the counter
            AOS.refresh();
            scrollRef.current = 0;
          }
        }, 200); // Adjust throttle delay as needed
      }
    };

    // Add event listeners for scroll and resize
    window.addEventListener("scroll", handleScrollOrResize);
    window.addEventListener("resize", handleScrollOrResize);

    // Cleanup on component unmount
    return () => {
      window.removeEventListener("scroll", handleScrollOrResize);
      window.removeEventListener("resize", handleScrollOrResize);
    };
  }, []);

  
  return (
    <React.Fragment>
      <Header />
      <main
        id="main-element"
      >
        {props.children}
      </main>
      <Footer />
    </React.Fragment>
  );
};

export default Layout;
