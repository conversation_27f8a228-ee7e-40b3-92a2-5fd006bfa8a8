$proxima-regular: "proxima_novaregular", "Almarai";

$proxima-light: "Proxima Nova Light", "Almarai";
$media-1440-min: "only screen and (min-width: 1440px)";
$media-1200-min: "only screen and (min-width: 1200px)";
$media-1599: "only screen and (max-width: 1599px)";
$media-1198: "only screen and (max-width: 1198px)";
$media-1200: "only screen and (max-width: 1200px)";

$media-1440: "only screen and (max-width: 1500px)";
$media-1400: "only screen and (max-width: 1400px)";
$media-1800: "only screen and (max-width: 1800px)";
$media-1600: "only screen and (max-width: 1600px)";
$media-min-1399: "only screen and (min-width: 1399px)";
$media-min-1366: "only screen and (min-width: 1366px)";
$media-min-600: "only screen and (min-width: 600px)";
$media-1366: "only screen and (max-width: 1395px)";
$media-1300: "only screen and (max-width: 1300px)";
$media-1280: "only screen and (max-width: 1280px)";
$media-1200: "only screen and (max-width: 1200px)";
$media-1024: "only screen and (max-width: 1024px)";
$media-995: "only screen and (max-width: 995px)";
$media-1199: "only screen and (max-width: 1199px)";
$media-1800: "only screen and (max-width: 1800px)";
$media-1000: "only screen and (max-width: 1000px)";
$media-min-1001: "only screen and (min-width: 1001px)";
$media-950: "only screen and (max-width: 950px)";
$media-768: "only screen and (max-width: 768px)";
$media-820: "only screen and (max-width: 820px)";
$media-min-820: "only screen and (min-width: 821px)";
$media-700: "only screen and (max-width: 700px)";
$media-600: "only screen and (max-width: 600px)";
$media-500: "only screen and (max-width: 500px)";
$media-480: "only screen and (max-width: 480px)";
$media-400: "only screen and (max-width: 400px)";
$media-320: "only screen and (max-width: 320px)";

// $proxima-medium: 'Almarai', 'Proxima Nova Medium';
// $proxima-semibold: 'Almarai', 'proxima_novasemibold';
// $proxima-bold: 'Almarai', 'Proxima Nova Bold';
// $proxima-regular: 'Almarai', 'proxima_novaregular';
// $source-sans: 'Almarai', 'source_sans_proregular';
// $source-semibold: 'Almarai' ,'source_sans_prosemibold';
// $source-light: 'Almarai' ,'source_sans_prolight';
// $proxima-light: 'Almarai' ,'Proxima Nova Light';
// $media-1599:"only screen and (max-width: 1599px)";
// $media-1440:"only screen and (max-width: 1440px)";
// $media-1366:"only screen and (max-width: 1366px)";
// $media-1200:"only screen and (max-width: 1200px)";
// $media-1024:"only screen and (max-width: 1024px)";
// $media-820:"only screen and (max-width: 820px)";
// $media-600:"only screen and (max-width: 600px)";
// $media-480:"only screen and (max-width: 480px)";

$red: #e7151a;
$white: #fff;
$gray: #d9d9d9;
$black: #000;
$blue: #5e45ff;
$blueh: rgb(52, 80, 219);
$blue2h: #2545e3;
$txt_clr: #febf99;