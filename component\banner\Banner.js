import React from "react";
import Link from "next/link";
import Image from "next/image";
import style from "@/component/banner/Banner.module.scss";
const Banner = ({ backgroundImg, banner_text }) => {
    return (
        <section data-aos="fade-in" data-aos-duration="700" className={style.banner_section}>
            <div className={style.banner_background}>
                <Image
                    src={`${backgroundImg}`}
                    alt="image"
                    height={500}
                    width={1000}
                    quality={100}
                />
                <h2 data-aos="fade-up" data-aos-duration="700">{banner_text}</h2>
            </div>
        </section>
    );
};

export default Banner;
