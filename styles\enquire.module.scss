@import "variable", "base";

.enquire_modal_body {
    width: 90%;
    max-width: 733px;
    background-color: #ffffff !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    border-radius: 15px;
    transform: translate(-50%, -50%) !important;
    padding: 45px 45px 60px 35px !important;

    @media #{$media-1600} {
        padding: 40px 40px 50px 30px !important;
    }

    @media #{$media-1280} {
        padding: 30px 30px 40px 30px !important;
    }

    @media #{$media-600} {
        padding: 20px 20px 30px 20px !important;
    }

    .modal_close {
        position: absolute;
        top: 20px;
        right: auto;
        inset-inline-end: 20px;
        border-radius: 50%;
        background-color: #805e59;

        svg {
            fill: #ffffff !important;
        }

        @media #{$media-950} {
            width: 30px;
            height: 30px;
        }

        &:hover {
            background-color: #5a3c37;
        }
    }

    h3 {
        color: #000000;
        font-size: 2.5rem;
        font-weight: 600;
        text-transform: capitalize
    }

    .enq_feild_list {
        margin-inline-start: 15px;

        @media #{$media-1600} {
            margin-inline-start: 15px;
        }

        @media #{$media-1200} {
            margin-inline-start: 0px;
        }

        li {
            width: 100%;


            &.two_col {
                display: flex;
                gap: 32px;

                @media #{$media-1600} {
                    gap: 25px;
                }
            }

            input:not([type="checkbox"]),
            select,
            textarea {
                border-radius: 30px;
                padding: 13px 20px;
                font-size: 1rem;
                width: 100%;
                border: 1px solid #d3d3d3;
                color: #000000;
                -webkit-appearance: none;
                appearance: none;

                @media #{$media-1280} {
                    font-size: 14px;
                }

                @media #{$media-500} {
                    font-size: 16px;
                }

                &::placeholder {
                    color: #000000;
                }

                &.textarea_field {
                    padding: 20px;
                    max-height: 200px;
                    min-height: 140px;
                    min-width: 100%;
                    max-width: 100%;
                    resize: none;

                    &::placeholder {
                        color: rgba(0, 0, 0, 0.5);
                    }

                }

                &:focus {
                    border: 1px solid #c09273;
                }
            }

            input,
            select {
                height: 50px;

            }

            select {
                @media #{$media-500} {
                    padding: 10px 20px;
                }
            }

            &.flex_end {
                display: flex;
                justify-content: flex-end;
            }

            &+li {
                margin-top: 38px;

                @media #{$media-1600} {
                    margin-top: 30px;
                }

                @media #{$media-1200} {
                    margin-top: 20px;
                }

                @media #{$media-950} {
                    margin-top: 15px;
                }
            }
        }
    }
}


.checkmark__circle {
    stroke-dasharray: 266;
    stroke-dashoffset: 266;
    stroke-width: 4;
    stroke-miterlimit: 20;
    stroke: #138831;
    fill: none;
    animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: block;
    stroke-width: 3;
    stroke: #138831;
    stroke-miterlimit: 10;
    margin: 30px auto;
    box-shadow: inset 0px 0px 0px #7ac142;
    animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    @media #{$media-950} {
        width: 70px;
        height: 70px;
    }
}

.checkmark__check {
    transform-origin: 50% 50%;
    stroke-dasharray: 48;
    stroke-dashoffset: 48;
    animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

@keyframes stroke {
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes scale {

    0%,
    100% {
        transform: none;
    }

    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 30px #fff;
    }
}

.modal_sucess_content {
    text-align: center;

    h5 {
        font-size: 2rem;
        line-height: 2.8rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: #138831;
        @media #{$media-950} {
            font-size: 2.4rem;
            line-height: 3rem;
        }
        @media #{$media-700} {
            font-size: 24px;
            line-height: 28px;
        }
    }

    h6 {
        color: #241f21;
        font-size: 1.2625rem;
        line-height: 2rem;
        font-weight: 400;
        margin-bottom: 5px;
        @media #{$media-950} {
            font-size: 1.9rem;
            line-height: 2.7rem;
        }
        @media #{$media-700} {
            font-size: 15px;
            line-height: 18px;
        }
    }
}