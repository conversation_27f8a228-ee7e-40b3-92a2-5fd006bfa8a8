@import "variable", "base";

.footer {
    background-color: #F5EDE6;

    .footer_detail {
        h4 {
            color: #241f21;
            font-size: 1.5625rem;
            font-weight: 500;
            margin-bottom: 5px;

            @media #{$media-768} {
                font-size: 2.5rem;
            }
        }

        p {
            color: #000000;
            font-size: 1.0625rem;
            font-weight: 300;

            @media #{$media-1200} {
                font-size: 14px;
            }
        }
    }

    form {
        @media #{$media-950} {
            width: 100%;
        }

        .subscribe {
            border: 1px solid #ffffff;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 30px;
            overflow: hidden;

            @media #{$media-950} {
                width: 100%;
                margin-bottom: 25px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            input {
                border: none;
                width: 423px;
                height: 100%;
                padding: 10px 25px;
                background: transparent;
                color: #574648;
                font-size: 13px;
                &::placeholder{
                    color: #574648;
                }
                @media #{$media-1440} {
                    width: 370px;
                }
                @media #{$media-950} {
                    font-size: 13px;
                    width: 100%;
                }
                @media #{$media-700} {
                    font-size: 16px;
                }
            }

            button {
                width: 216px;
                font-family: var(--font-primary), var(--font-primary-ar) ;
                font-size: 1rem;
                line-height: 1.75rem;
                font-weight: 300;
                color: #F5EDE6;
                @media #{$media-1440} {
                    width: 180px;
                }

                @media #{$media-1200} {
                    font-size: 14px;
                }

            }
        }
    }

    .pattern_wrap {
        margin-top: 57px;
        position: relative;
        width: 100%;
        height: 21px;
        @media #{$media-700} {
            margin-top: 30px;
        }

        img {
            object-fit: cover;
        }

    }

    .footer_bottom {
        margin-top: 33px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        @media #{$media-700} {
            justify-content: flex-start;
            gap: 5px;
        }

        p {
            color: #574648;
            font-size: 0.875rem;
            line-height: 1.5rem;
            font-weight: 300;
            margin-bottom: 0;

            @media #{$media-1200} {
                font-size: 13px !important;
            }
            @media #{$media-700} {
               order: 3;
            }
        }

        .managed_img {
            display: flex;
            align-items: center;
        }

        .quick_links {
            margin: 0 -12px;
            @media #{$media-950} {
                margin: 0 auto;
            }
            @media #{$media-700} {
                margin:unset;
                margin-bottom: 10px;
                line-height: unset;
               
            }

            a {
                color: #574648;
                font-size: 0.875rem;
                line-height: 150%;
                font-weight: 400;
                text-decoration: underline;
                text-transform: uppercase;
                margin: 0 12px;

                @media #{$media-1200} {
                    font-size: 13px;
                }

                @media #{$media-600} {
                    font-size: 12px;
                    margin:unset;
                    margin-inline-end: 6px;
                }
                @media #{$media-400} {
                    font-size: 11px;
                }
                
            }
        }


    }
}
.social_media{
    display: flex;
    gap: 10px;
}