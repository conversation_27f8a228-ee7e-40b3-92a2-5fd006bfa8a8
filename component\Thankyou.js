import React, { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import style from "@/styles/enquire.module.scss";
import AOS from "aos";
import "aos/dist/aos.css";
import { useRouter } from "next/router";

const ThankyouModal = ({ open, onClose }) => {
  const router = useRouter();
  useEffect(() => {
    AOS.init({
      easing: "ease-out",
      duration: 1000,
    });
    //   console.log("Modal Data:", data);
  }, []);
  useEffect(() => {
    if (open) {
      const timer = setTimeout(() => {
        onClose();
      }, 4000); // Close after 2 seconds

      // Cleanup the timer when the component unmounts or open changes
      return () => clearTimeout(timer);
    }
  }, [open, onClose]);
  return (
    <Modal
      open={open}
      onClose={onClose}
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <div className={style.enquire_modal_body}>
        {/* <ModalClose
          variant="plain"
          className={style.modal_close}
          onClick={onClose}
        /> */}
        <div>
          <svg
            className={style.checkmark}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 52 52"
          >
            <circle
              className={style.checkmark__circle}
              cx="26"
              cy="26"
              r="25"
              fill="none"
            />
            <path
              class={style.checkmark__check}
              fill="none"
              d="M14.1 27.2l7.1 7.2 16.7-16.8"
            />
          </svg>
        </div>
        <div className={style.modal_sucess_content}>
          {router.locale == "ar" ? (
            <h5>شكرا لتواصلك معنا</h5>
          ) : (
            <h5>Thank you for submitting</h5>
          )}
          {router.locale == "ar" ? (
            <h6> فريقنا سيتواصل معك في أقرب وقت</h6>
          ) : (
            <h6>Our team will be in touch with you soon</h6>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ThankyouModal;
