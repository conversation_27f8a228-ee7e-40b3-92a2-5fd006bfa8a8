import React, { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import style from "@/styles/enquire.module.scss";
import common from "@/styles/comon.module.scss";
import AOS from "aos";
import "aos/dist/aos.css";
import parse from "html-react-parser";
import Academyform from "@/component/Academyform"

const EnquireModal = ({ open, onClose, data,Formfeild }) => {
  useEffect(() => {
    AOS.init({
      easing: "ease-out",
      duration: 1000,
    });
 //   console.log("Modal Data:", data);
  }, []);

  return (
    <Modal
      open={open}
      onClose={onClose}
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >

      <div className={style.enquire_modal_body}>
        <ModalClose
          variant="plain"
          className={style.modal_close}
          onClick={onClose}
        />
        <h3>{Formfeild?.enquire_now_title && parse(Formfeild?.enquire_now_title)}</h3>
         <Academyform crycform={Formfeild} page_title={data?.coaching_title ? data?.coaching_title : data?.packages_title } />
        
      </div>
    </Modal>
  );
};

export default EnquireModal;
