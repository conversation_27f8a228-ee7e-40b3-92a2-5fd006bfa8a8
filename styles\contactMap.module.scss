@import "variable", "base";

.mapContainer {
    position: relative;
    width: 100%;
    height: 749px; // Adjust height as needed
    @media #{$media-1600} {
        height: 615px;
    }
    @media #{$media-1200} {
        height: 550px;
    }
    @media #{$media-700} {
        height: 550px;
    }
}

.overlayText {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 10%;
    color: #0c0c0c;
    text-align: center;
    font-family: var(--font-primary), var(--font-primary-ar);
    font-size: 3.438rem;
    line-height: 121%;
    font-weight: 700;
    pointer-events: none; // Ensures the text doesn't block interaction with the map
    @media #{$media-1440} {
        font-size: 3.3rem;
    
    }
}