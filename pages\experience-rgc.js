import React from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import style from "@/styles/becomeMember.module.scss";
import Banner from "@/component/banner/Banner";
import DynamicImage from "@/component/DynamicImage";
import GolfersSay from "@/component/GolfersSay";
import Insta from "@/component/Insta";
import { useRouter } from "next/router";
import GolfTechnologySection from '@/component/GolfTechnologySection/GolfTechnologySection';

import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getExperiencepage,getInstaPosts,getTestimonialPosts } from "@/utils/lib/server/publicServices";


const becomeMember = (props) => {
  const { locale } = useRouter();

  const yoastData = props?.ExperData?.yoast_head_json;

  if (!props?.ExperData) {
    return null;
  }

  return (
    <div>
       {yoastData && <Yoast meta={yoastData} />}
            
      {props &&
        props?.ExperData &&
        props?.ExperData?.acf &&
        (props?.ExperData?.acf?.banner_image || props?.ExperData?.title || props?.ExperData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.ExperData?.acf?.banner_image?.url}
                banner_text={parse(props?.ExperData?.acf?.banner_title || props?.ExperData?.title?.rendered)}
            />
      ) : null}


       <GolfTechnologySection
        sectionTitle={props?.ExperData?.acf?.section_title}
        sectionSubtitle={props?.ExperData?.acf?.section_subtitle}
        technologies={props?.ExperData?.acf?.golf_technology_cards} />
      
{props &&
        props?.ExperData &&
        props?.ExperData?.acf &&
        (props?.ExperData?.acf?.night_title ||
          props?.ExperData?.acf?.night_content ||
          props?.ExperData?.acf?.night_button ||
          props?.ExperData?.acf?.night_image) ? (
      <section
            className={`${common.d_flex} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
            id="night-golf"
      >
        <div className={`${common.container}`}>
          <div className={`${common.corporate_golf_inner} ${common.justify_space_bet}`}>
            <div
              className={`${common.corporate_content_box} ${common.corporate_content_box_2}`}
                >
                  {props?.ExperData?.acf?.night_title &&
                    <div className={` ${common.title_h1}`}>
                      <h3 data-aos="fade-up" data-aos-duration="700">
                        {props?.ExperData?.acf?.night_title && parse(props?.ExperData?.acf?.night_title)}
                      </h3>
                    </div>
                  }
                  {props?.ExperData?.acf?.night_content &&
                    <div data-aos="fade-up" data-aos-duration="700">
                      {props?.ExperData?.acf?.night_content && parse(props?.ExperData?.acf?.night_content)}
                    </div>
                  }
                  {props?.ExperData?.acf?.night_button &&
                    <Link
                      href={props?.ExperData?.acf?.night_button?.url}
                      target={props?.ExperData?.acf?.night_button?.target}
                      className={`${common.btn_link} ${common.btn_link_2} ${common.mt_20}`}
                      data-aos="fade-up"
                      data-aos-duration="700"
                    >
                      <label>{props?.ExperData?.acf?.night_button && parse(props?.ExperData?.acf?.night_button?.title)}</label>
                      <span>
                        <Image
                          src="/images/btn-img.svg"
                          width={30}
                          height={30}
                          alt="button image"
                        />
                      </span>
                    </Link>
                  }
            </div>
{props?.ExperData?.acf?.night_image &&
            <div
              className={`${common.corporate_slider_box} ${common.image_block_style} ${common.corporate_slider_box_2} ${common.p_relative}`}
            >
              <div  data-aos="fade-up" data-aos-duration="700" className={`  ${common.corporate_slider_img_h} ${common.fill_image_wrapper}`}>


                <Image
                  src={props?.ExperData?.acf?.night_image?.url}
                  fill
                  style={{ objectFit: 'cover' }}
                  alt={props?.ExperData?.acf?.night_image?.alt || 'image'}
                   sizes="(max-width: 768px) 100vw"
                />
                <Image
                  className={`${common.w_100} ${common.holder}`}
                  src="/images/holder_7.jpg"
                  width={672}
                  height={447}
                  alt="button image"
                  style={{ height: 'auto', width: '100%', display: 'block' }}
                />
              </div>
                </div>
}
          </div>
        </div>
      </section>
      ) : null}

{props &&
        props?.ExperData &&
        props?.ExperData?.acf &&
        (props?.ExperData?.acf?.pro_shop_title ||
          props?.ExperData?.acf?.pro_shop_content ||
          props?.ExperData?.acf?.pro_shop_image) ? (
        <section className={` ${common.pt_200} ${common.pb_200}`} style={{
          background: `url(${props?.ExperData?.acf?.pro_shop_image?.url}) no-repeat center/cover`,
        }} id="pro-shop">
          <div className={`${common.container}`}>

              <div className={` ${common.w_50} ${common.white_txt}`}>
                 {props?.ExperData?.acf?.pro_shop_title &&
                    <div className={` ${common.title_h1}`}>
                      <h3 data-aos="fade-up" data-aos-duration="700">
                        {props?.ExperData?.acf?.pro_shop_title && parse(props?.ExperData?.acf?.pro_shop_title)}
                      </h3>
                    </div>
                  }
                  {props?.ExperData?.acf?.pro_shop_content &&
                    <div data-aos="fade-up" data-aos-duration="700">
                      {props?.ExperData?.acf?.pro_shop_content && parse(props?.ExperData?.acf?.pro_shop_content)}
                    </div>
                  }
             
            </div>
          </div>
        </section>
      ) : null}

{props &&
        props?.ExperData &&
        props?.ExperData?.acf &&
        (props?.ExperData?.acf?.dining_title ||
          props?.ExperData?.acf?.dining_content ||
          props?.ExperData?.acf?.dining_image) ? (
        <section
            className={`${common.d_flex} ${common.corporate_section_padding_mobile} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
            id="dining"
            style={{ backgroundColor: "#F5EDE6" }}
        >
          <div className={`${common.container}`}>
            <div className={`${common.corporate_golf_inner} ${common.justify_space_bet}`}>
              <div className={`${common.corporate_content_box} ${common.dining}  ${common.corporate_content_box_w_2}`}
                >
                  
                  {props?.ExperData?.acf?.dining_title &&
                    <div className={` ${common.title_h1}`}>
                      <h3 data-aos="fade-up" data-aos-duration="700">
                        {props?.ExperData?.acf?.dining_title && parse(props?.ExperData?.acf?.dining_title)}
                      </h3>
                    </div>
                  }
                  {props?.ExperData?.acf?.dining_content &&
                    <div data-aos="fade-up" data-aos-duration="700">
                      {props?.ExperData?.acf?.dining_content && parse(props?.ExperData?.acf?.dining_content)}
                    </div>
                  }
                  {/* Menu btn */}
                   {props?.ExperData?.acf?.dine_button &&
                    <Link
                      href={props?.ExperData?.acf?.dine_button?.url}
                      target={props?.ExperData?.acf?.dine_button?.target}
                      className={`${common.btn_link} ${common.btn_link_2} ${common.mt_20}`}
                      data-aos="fade-up"
                      data-aos-duration="700"
                    >
                      <label>{props?.ExperData?.acf?.dine_button?.title && parse(props?.ExperData?.acf?.dine_button?.title)}</label>
                      <span>
                        <Image
                          src="/images/btn-img.svg"
                          width={30}
                          height={30}
                          alt="button image"
                        />
                      </span>
                    </Link>
                  }
                   
                  


              </div>
              {props?.ExperData?.acf?.dining_image &&
                <div className={`${common.corporate_slider_box} ${common.p_relative}`} >
                    <ul className={`${common.temp_style_01}`}>
                      {props?.ExperData?.acf?.dining_image &&
                            props?.ExperData?.acf?.dining_image.map((privates, pindex) => (
                            <li data-aos="fade-up" data-aos-duration="700" key={pindex}>
                              <div className={`${common.image_block_style}  ${common.p_relative} ${common.fill_image_wrapper} ${pindex === 2 ? common.h_dining_block_2 : common.h_dining_block}`}>                            
                                <Image
                                  src={privates?.image_slide?.url}
                                  fill
                                  style={{ objectFit: 'cover' }}
                                  alt="image"
                                   sizes="(max-width: 768px) 100vw"
                                />

                                <Image
                                  className={`${common.w_100} ${common.holder}`}
                                  src={pindex === 2 ? "/images/holder_9b.jpg" : "/images/holder_9a.jpg" }
                                  width={672}
                                  height={447}
                                  alt="button image"
                                  style={{ height: 'auto', width: '100%', display: 'block' }}
                                />
                              </div>
                            </li>
                        ))}                        
                    </ul>                  
                </div>
              }

           
            </div>
          </div>
        </section>
      ) : null}

      {props &&
        props?.ExperData &&
        props?.ExperData?.acf &&
        (props?.ExperData?.acf?.health_title ||
          props?.ExperData?.acf?.health_content ||      
          props?.ExperData?.acf?.health_image_gallery) ? (
        <section  className={`${common.d_flex} ${common.pt_100} ${common.pb_100}`} >
          <div className={`${common.container}`}>
            <div className={`${common.welcome_txt_block} `}>
                <div className={`${common.title_h1} ${common.text_center}`}>
                  {props?.ExperData?.acf?.health_title &&
                    <h2 data-aos="fade-up" data-aos-duration="700">
                      {props?.ExperData?.acf?.health_title && parse(props?.ExperData?.acf?.health_title)}
                    </h2>
                  }
                  {props?.ExperData?.acf?.health_content &&
                  <div data-aos="fade-up" data-aos-duration="700">
                      {parse(props?.ExperData?.acf?.health_content)}
                    </div>
                  }
              </div>
            </div>
              {props?.ExperData?.acf?.health_image_gallery &&
                <ul className={`${common.card_group_sec} ${common.pt_70}`}>
                  {props?.ExperData?.acf?.health_image_gallery &&
                    props?.ExperData?.acf?.health_image_gallery.map((health, hindex) => (           
                  <li className={`${common.card_box} ${common.mouse_default}`} key={hindex} data-aos="fade-up" data-aos-duration="700">
                    <div className={common.card_img}>
                      <div className={`${common.image_block_style} ${common.p_relative} ${common.fill_image_wrapper} ${common.h_experience_block} `}>
                        <Image
                          src={health?.url}
                          fill
                          style={{ objectFit: 'cover' }}
                          alt="image"
                           sizes="(max-width: 768px) 100vw"
                        />
                        <Image
                          className={`${common.w_100} ${common.holder}`}
                          src="/images/holder_8.jpg"
                          width={672}
                          height={447}
                          alt="button image"
                          style={{ height: 'auto', width: '100%', display: 'block' }}
                        />
                      </div>
                    </div>
                  </li>
                    ))}                  
                </ul>
              }
          </div>
        </section>
      ) : null}
      

{props &&
        props?.ExperData &&
        props?.ExperData?.acf &&
        (props?.ExperData?.acf?.padel_title ||
          props?.ExperData?.acf?.padel_content ||
          props?.ExperData?.acf?.padel_button ||
          props?.ExperData?.acf?.day_and_time ||
          props?.ExperData?.acf?.packages ||
          props?.ExperData?.acf?.padel_images) ? (

      <section style={{ backgroundColor: "#F5EDE6" }} className={`${common.d_flex} ${common.corporate_section_padding_mobile} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
      id="padel"
          >
        <div className={`${common.container}`}>
          <div className={`${common.corporate_golf_inner} ${common.justify_space_bet} ${common.row_reverse}`}>
            <div className={`${common.corporate_content_box} ${common.corporate_content_box_w_3}`}
            >
              {props?.ExperData?.acf?.padel_title &&
                    <div className={` ${common.title_h1}`}>
                      <h3 data-aos="fade-up" data-aos-duration="700">
                        {props?.ExperData?.acf?.padel_title && parse(props?.ExperData?.acf?.padel_title)}
                      </h3>
                    </div>
                  }
                  {props?.ExperData?.acf?.padel_content &&
                    <div data-aos="fade-up" data-aos-duration="700">
                      {props?.ExperData?.acf?.padel_content && parse(props?.ExperData?.acf?.padel_content)}
                    </div>
                  }
              
                  {(props?.ExperData?.acf?.day_and_time || props?.ExperData?.acf?.packages) &&
                    <ul className={`${common.time_table_block}`} >
                      {props?.ExperData?.acf?.day_and_time &&
                         <li data-aos="fade-up" data-aos-duration="700">
                          <div className={`${common.time_table_block_2}`}>
                           
                            {props?.ExperData?.acf?.day_and_time &&
                              props?.ExperData?.acf?.day_and_time.map((date, dindex) => (
                                <React.Fragment key={dindex}>
                                  {dindex % 2 === 1 && ( // Show only for even indices
                                    <div className={`${common.time_table_block_4}`} >
                                      <Image
                                        src="/images/center_img.svg"

                                        width={12}
                                        height={41}
                                        alt="logo"
                                      />
                                    </div>
                                    )}
                                  <div className={`${common.time_table_block_3}`} >
                                    <Image
                                      src="/images/clock.svg"

                                      width={32}
                                      height={32}
                                      alt="logo"
                                    />

                                    <p>
                                      <b>{date.day && parse(date.day)}</b> <br />
                                      {date.time && parse(date.time)}
                                    </p>
                                  </div>

                                  </React.Fragment>                           
                                
                              ))} 
                          </div>
                        </li>
                      }
                      {props?.ExperData?.acf?.packages &&
                        props?.ExperData?.acf?.packages.map((pack, paindex) => (
                          <li key={paindex}>
                            <div className={`${common.time_table_block_2}`}>
                              <p>{pack.packages_title}<br />
                                <b>{pack.packages_price}</b>
                              </p>
                            </div>
                          </li>
                        ))}

                     
                    </ul>
                  }
                  {props?.ExperData?.acf?.padel_button &&
                    <Link
                      href={props?.ExperData?.acf?.padel_button?.url}
                      target={props?.ExperData?.acf?.padel_button?.target}
                      className={`${common.btn_link} ${common.btn_link_2} ${common.mt_40}`}
                      data-aos="fade-up"
                      data-aos-duration="700"
                    >
                      <label>{props?.ExperData?.acf?.padel_button?.title && parse(props?.ExperData?.acf?.padel_button?.title)}</label>
                      <span>
                        <Image
                          src="/images/btn-img.svg"
                          width={30}
                          height={30}
                          alt="button image"
                        />
                      </span>
                    </Link>
                  }


            </div>

            
           
              {props?.ExperData?.acf?.padel_images &&
                <div className={`${common.corporate_slider_box} ${common.p_relative}`} >
                    <ul className={`${common.temp_style_01}`}>
                      {props?.ExperData?.acf?.padel_images &&
                            props?.ExperData?.acf?.padel_images.map((privates, pkindex) => (
                            <li data-aos="fade-up" data-aos-duration="700" key={pkindex}>
                              <div className={`${common.image_block_style}  ${common.p_relative} ${common.fill_image_wrapper} ${pkindex === 2 ? common.h_dining_block_2 : common.h_dining_block}`}>                            
                                <Image
                                  src={privates?.image_slide?.url}
                                  fill
                                  style={{ objectFit: 'cover' }}
                                  alt="image"
                                   sizes="(max-width: 768px) 100vw"
                                />

                                <Image
                                  className={`${common.w_100} ${common.holder}`}
                                  src={pkindex === 2 ? "/images/holder_9b.jpg" : "/images/holder_9a.jpg" }
                                  width={672}
                                  height={447}
                                  alt="button image"
                                  style={{ height: 'auto', width: '100%', display: 'block' }}
                                />
                              </div>
                            </li>
                        ))}                        
                    </ul>                  
                </div>
              }
           
            
          </div>
        </div>
      </section>
) : null}
      
     

     {props &&
    props?.ExperData &&
        props?.ExperData?.acf &&
        props?.ExperData?.acf?.testimonials?.length > 0 &&
        (props?.ExperData?.acf?.testimonial_title || 
      props?.TestimonialPostData ||  
      props?.ExperData?.acf?.testimonials) ? (

          <GolfersSay
            sec_title={props?.ExperData?.acf?.testimonial_title}
            sec_data={props?.TestimonialPostData}
          />
      ) : null }


    {props &&
        props?.ExperData &&
        props?.ExperData?.acf &&        
        (props?.ExperData?.acf?.instagram_title || 
      props?.ExperData?.acf?.instagram_tag ||  
      props?.ExperData?.acf?.instagram_icon) ? (
      <Insta
        Title={props?.ExperData?.acf?.instagram_title}
        Icon={props?.ExperData?.acf?.instagram_icon}
        Tag={props?.ExperData?.acf?.instagram_tag}
        insta_data ={props?.InstaPostsData}
      />
) : null }


    </div>
  );
};

export default becomeMember;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const ExperData = await getExperiencepage(locale); 
   const query = "";
  const TestimonialPostsData = await getTestimonialPosts(locale, query);
  const InstaPostsData = await getInstaPosts(locale, query);
  
  //console.log('data',ExperData)
   let TestimonialPosts = [];
      if (ExperData && ExperData?.acf && Array.isArray(ExperData?.acf?.testimonials)) {
      TestimonialPosts = ExperData?.acf?.testimonials;
      }

      // Format Investors Resources  for use in the component
     let TestimonialPostsRelation = [];
      if (TestimonialPosts.length > 0) {
      TestimonialPostsRelation = TestimonialPosts.map((id) =>
        TestimonialPostsData?.find((post) => post.id === id)
      ).filter(Boolean); // To ensure undefined values (if any) are removed
      }
        // console.log('ExperData', TestimonialPostsRelation)
        
  return {
    props: {
      ExperData: ExperData || null,   
      TestimonialPostData: TestimonialPostsRelation || [],
      InstaPostsData: InstaPostsData || []
    },
    revalidate: 10,
  };
};