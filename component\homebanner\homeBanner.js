import React from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import styles from "./homeBanner.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, EffectFade, Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/effect-fade";
import "swiper/css/pagination";
import { useRouter } from "next/router";
import useResponsive from "@/component/useResponsive";

import parse from "html-react-parser";

const HomeBanner = ({ sliders }) => {
    const router = useRouter();
    const isMobile = useResponsive();
  return (
    <section className={`${styles.banner_main} banner_main `}>
      <div className={styles.banner_swiper_navigation}>
        <span className={`swiper-button-next ${styles.hm_banner_next} ${styles.hm_swipper_arrow}`}> </span>
        <span className={`swiper-button-prev ${styles.hm_banner_prev} ${styles.hm_swipper_arrow}`}> </span>
      </div>

      <Swiper
        spaceBetween={0}
        loop={true}
        slidesPerView={1}
        speed={1000}
        key={router.locale} // Use router locale here
        dir={router.locale === "ar" ? "rtl" : "ltr"}
        effect={"fade"}
        parallax={true}
        pagination={{ clickable: true }}
        modules={[Pagination, EffectFade, Navigation]}
        className={styles.main_slider}
        navigation={{
          prevEl: ".swiper-button-prev",
          nextEl: ".swiper-button-next",
        }}
      >
        {sliders &&
          sliders.map((slidelist, slideindex) => (
            <SwiperSlide key={slideindex}>
              <div className={`container ${styles.h_100} ${styles.p_relative}`}>
                <div className={styles.banner_txt}>
                  {slidelist.banner_text &&
                    <h1>
                      {parse(slidelist.banner_text)}
                    </h1>
                  }
                  {slidelist.learn_more_button &&
                    <div className={styles.banner_button_blok}>
                      <ul>
                        <li>
                          <Link
                            href={slidelist.learn_more_button?.url}
                            target={slidelist.learn_more_button?.target}
                            className={`${common.btn_link} ${common.btn_link_2}`}
                          >
                            <label>{slidelist.learn_more_button?.title && parse(slidelist?.learn_more_button?.title)}</label>
                            <span>
                              <img src="/images/btn-img.svg" alt=" "/>
                            </span>
                          </Link>
                        </li>
                      </ul>
                    </div>
                  }
                </div>
              </div>
              {slidelist.banner_type === 'video' ? (
                    slidelist.video_type === 'file' ? (
                      isMobile ? (
                        <video
                          className={styles.banner_video}
                          src={slidelist?.mobile_video?.url || slidelist?.banner_video?.url}
                          autoPlay
                          loop
                          muted
                          playsInline
                        />
                      ) : (
                        <video
                          className={styles.banner_video}
                          src={slidelist?.banner_video?.url}
                          autoPlay
                          loop
                          muted
                          playsInline
                        />
                      )
                    ) : slidelist.video_type === 'url' ? (
                      isMobile ? (
                        <video
                          className={styles.banner_video}
                          src={slidelist?.mobile_video_url || slidelist?.main_video_url}
                          autoPlay
                          loop
                          muted
                          playsInline
                        />
                      ) : (
                        <video
                          className={styles.banner_video}
                          src={slidelist?.main_video_url}
                          autoPlay
                          loop
                          muted
                          playsInline
                        />
                      )
                    ) : null
                  ) : (
                    <Image src={slidelist?.banner_image?.url} fill alt="banner" />
                  )}
             
            </SwiperSlide>
          ))}
       
      </Swiper>
    </section>
  );
};

export default HomeBanner;
