// components/CustomMap.js
import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow, useLoadScript } from "@react-google-maps/api";
import style from "@/styles/contactMap.module.scss";
import parse from "html-react-parser";

const mapStyles = [
    {
        "featureType": "administrative",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "on"
            },
            {
                "lightness": 33
            }
        ]
    },
    {
        "featureType": "landscape",
        "elementType": "all",
        "stylers": [
            {
                "color": "#f7f7f7"
            }
        ]
    },
    {
        "featureType": "poi.business",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "poi.park",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#deecdb"
            }
        ]
    },
    {
        "featureType": "poi.park",
        "elementType": "labels",
        "stylers": [
            {
                "visibility": "on"
            },
            {
                "lightness": "25"
            }
        ]
    },
    {
        "featureType": "road",
        "elementType": "all",
        "stylers": [
            {
                "lightness": "25"
            }
        ]
    },
    {
        "featureType": "road",
        "elementType": "labels.icon",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "road.highway",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#ffffff"
            }
        ]
    },
    {
        "featureType": "road.highway",
        "elementType": "labels",
        "stylers": [
            {
                "saturation": "-90"
            },
            {
                "lightness": "25"
            }
        ]
    },
    {
        "featureType": "road.arterial",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "on"
            }
        ]
    },
    {
        "featureType": "road.arterial",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#ffffff"
            }
        ]
    },
    {
        "featureType": "road.local",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#ffffff"
            }
        ]
    },
    {
        "featureType": "transit.line",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "transit.station",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "water",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "on"
            },
            {
                "color": "#e0f1f9"
            }
        ]
    }
]

const containerStyle = {
  width: "100%",
  height: "100%", // Full height
};

const center = {
  lat: 24.9593096417886, // Replace with your latitude24.9593096417886
  lng: 46.57596413714685, // Replace with your longitude
};

const CustomMap = ({Mappoints,banner_text}) => {
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
  });

    const [activeMarker, setActiveMarker] = useState(null);

    const handleMarkerClick = (index) => {
        setActiveMarker(index);
    };

  const handleInfoWindowClose = () => {
    setActiveMarker(null);
    };
    
  if (!isLoaded) return <div>Loading...</div>;

  return (
      <div className={style.mapContainer}>
          {Mappoints &&
              <GoogleMap
                  mapContainerStyle={containerStyle}
                  center={center}
                  zoom={12}
                  options={{
                      styles: mapStyles,
                      disableDefaultUI: true, // Hides default UI
                  }}
              >
                  {/* <Marker position={center} /> */}
                  {Mappoints && Mappoints.map((point, index) => (
                      <Marker
                          key={index}
                          position={{
                              lat: parseFloat(point?.latitude),
                              lng: parseFloat(point?.longitude),
                          }}
                          onClick={() => handleMarkerClick(index)}
                          icon={{
                            url: "/images/map_point.svg", // White marker icon
                            scaledSize: new window.google.maps.Size(37, 45), // Adjust size if needed
                          }}
                      >
                          {activeMarker === index && (
                              <InfoWindow onCloseClick={handleInfoWindowClose}>
                                  <div>
                                      {point?.location_name &&
                                          <h3>{parse(point?.location_name)}</h3>
                                      }
                                      {point?.location_address &&
                                          <p>{parse(point?.location_address)}</p>
                                      }
                                      {point?.get_direction &&
                                          <a href={point?.get_direction?.url} target="_blank" rel="noopener noreferrer">
                                              {point?.get_direction?.title && parse(point?.get_direction?.title)}
                                          </a>
                                      }
                                  </div>
                              </InfoWindow>
                          )}
                      </Marker>
                  ))}
              </GoogleMap>
          }
      
          <div className={style.overlayText}>{banner_text && parse(banner_text)}</div>
    </div>
  );
};

export default CustomMap;
