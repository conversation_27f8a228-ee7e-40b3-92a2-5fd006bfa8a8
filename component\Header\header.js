import React, { useEffect, useState, useRef } from "react";
import styles from "./header.module.scss";
import common from "@/styles/comon.module.scss";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/router";
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Footer = (props) => {
  //---------Setting language ------ end
  const [language, setLanguage] = useState("");
  const [headoption, setOptions] = useState(null);
  const router = useRouter();
  const { pathname, asPath } = router;
  const targetLocale = router.locale === "en" ? "ar" : "en";
  const menuRef = useRef(null); 
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {

    const fetchMyAcfOptions = async (locale) => {
      try {
        const HeaderPostsData = await getThemeoptions(locale);
       // console.log("header options:", HeaderPostsData);
        setOptions(HeaderPostsData);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };
    fetchMyAcfOptions(router.locale);

    if (router && router.locale == "ar") {
      setLanguage("ar");

      document.body.classList.add("rtl", common.rtl);
      document.documentElement.setAttribute("dir", "rtl");
      document.documentElement.setAttribute("lang", "ar");
    } else {
      setLanguage("en");
      document.body.classList.remove("rtl", common.rtl);
      document.documentElement.setAttribute("dir", "ltl");
      document.documentElement.setAttribute("lang", "en");
    }
  }, [router]);
  
  const [mobMenu, setMobmenu] = useState(false);
  const menuOpen = () => {
    setMobmenu((prev) => !prev);


  };

  const menuClose = () => setMobmenu(false)

  useEffect(() => {
    const handleScroll = () => {
        // Check if .main_head has scrolled out of view
        if (menuRef.current) {
            const headerOffset = menuRef.current.getBoundingClientRect().top;
            setIsSticky(window.scrollY > headerOffset); // Set sticky if page is scrolled past header
        }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
        window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  

  if (!headoption) {
    return null;
  }

  return (
    <>
      {/* style={{backgroundImage: 'url("/images/golf_logo.png")',
    backgroundSize: 'cover',  
    backgroundPosition: 'center', 
    backgroundRepeat: 'no-repeat'  
  }} */}
 <header className={`${styles.header}  ${mobMenu == true ? styles.active : styles.close} ${isSticky ? styles.sticky : ''}`} ref={menuRef}>
        <div className="container">
          <div className={`${styles.header_inner} `}>
            <Link href={"/"}>
            <div className={styles.main_logo}>
              <div className={styles.logo_left}>
                <Image
                  src="/images/logo_img_text_01.png"
                  className={styles.logo_img_text_01}
                  width={52}
                  height={14}
                  alt="logo"
                />
                <Image
                  src="/images/logo_img_text_02.png"
                  className={styles.logo_img_text_02}
                  width={52}
                  height={14}
                  alt="logo"
                />
                <Image
                  src="/images/logo_img_text_03.png"
                  className={styles.logo_img_text_03}
                  width={52}
                  height={13}
                  alt="logo"
                />
              </div>
              <div className={styles.logo_center}>
                <Image
                  src="/images/logo_ing_01.png"
                  className={styles.logo_img_1}
                  width={20}
                  height={10}
                  alt="logo"
                />
                <Image
                  src="/images/logo_img_02.png"
                  className={styles.logo_img_2}
                  width={43}
                  height={29}
                  alt="logo"
                />
                <Image
                  src="/images/logo_img_03.png"
                  className={styles.logo_img_3}
                  width={32}
                  height={19}
                  alt="logo"
                />
              </div>
              <div className={styles.logo_right}>
                <Image
                  src="/images/logo_img_text_04.png"
                  className={styles.logo_img_text_04}
                  width={88}
                  height={7}
                  alt="logo"
                />
                <Image
                  src="/images/logo_img_text_05.png"
                  className={styles.logo_img_text_05}
                  width={88}
                  height={24}
                  alt="logo"
                />
              </div>
            </div>
            </Link>
            <div
              className={`${styles.header_menu_block} header_menu_block ${mobMenu == true ? styles.active : ""
                }`}
            >
              <ul className={`${styles.header_main_menu} header_main_menu`}>
                 {headoption?.main_menu && headoption?.main_menu.map((Mainmenu, mindex) => (
                  <li key={mindex}>
                     <Link href={Mainmenu?.main_nav?.url}
                       onClick={menuClose}
                      className="active"
                      target={Mainmenu?.main_nav?.target}>
                      {Mainmenu?.main_nav?.title && parse(Mainmenu?.main_nav?.title)}
                    </Link>
                  </li>
                )
                )}
                
              </ul>
            </div>

            <div className={`${styles.header_lang_block} header_lang_block`}>
              {headoption?.enquire_now && (
                <Link
                   href={headoption?.enquire_now?.url}
                  target={headoption?.enquire_now?.target}
                  className={`${common.white_border_btn} ${styles.hedder_buttion
                    } ${mobMenu == true ? common.active : ""}`}
                >
                  <span>{headoption?.enquire_now?.title && parse(headoption?.enquire_now?.title)}</span>
                </Link>
              )}
              {headoption?.language_name &&
                <Link
                  href={asPath}
                  locale={targetLocale}
                  className={`${styles.lang_txt} ${common.lang_txt} ${mobMenu == true ? common.active : ""
                    }`}
                >
                  {headoption?.language_name &&
                    parse(headoption?.language_name)}
                </Link>
              }

              <div
                className={`${styles.nav_container} ${mobMenu == true ? styles.is_active : ""
                  }   `}
                onClick={menuOpen}
              >
                <div className={styles.nav_toggle}></div>
              </div>
            </div>
          </div>
        </div>
      </header>
      
    </>
  );
};

export default Footer;