import Banner from "@/component/banner/Banner";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getCookiespage } from "@/utils/lib/server/publicServices";

const corporateEvents = (props) => {


  const yoastData = props?.EventsData?.yoast_head_json;

  if (!props?.EventsData) {
    return null;
  }

  
    return (
       
        <div>

            {yoastData && <Yoast meta={yoastData} />}
            
      {props &&
        props?.EventsData &&
        props?.EventsData?.acf &&
        (props?.EventsData?.acf?.banner_image || props?.EventsData?.title || props?.EventsData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.EventsData?.acf?.banner_image?.url}
                banner_text={parse(props?.EventsData?.acf?.banner_title || props?.EventsData?.title?.rendered)}
            />
        ) : null }
            
            {props &&
                props?.EventsData &&
                props?.EventsData?.content && (
                 
                    <section
                        className={`${common.corporate_events_section} ${common.side_line} ${common.pt_100}`}
                    >
                        <div className={`${common.container} ${common.pb_100} ${common.privacy_policy}`}>
                            
                            {props?.EventsData?.content &&
                                parse(props?.EventsData?.content?.rendered)
                            }
                        </div>
                        
                    </section >
                )}
           
        </div >
    );
};

export default corporateEvents;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const CookieData = await getCookiespage(locale);   
  //console.log('data',EventsData)
  return {
    props: {
      EventsData: CookieData || null,      
    },
    revalidate: 10,
  };
};