import React from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import Banner from "@/component/banner/Banner";
import DynamicImage from "@/component/DynamicImage";
import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { parseISO, isBefore, isAfter, format } from "date-fns";
import { ar } from "date-fns/locale";
import { getThemeoptions, getTournamentpage, getTournamentPosts } from "@/utils/lib/server/publicServices";
import { useRouter } from 'next/router';


function getOrdinalSuffix(day) {
  if (day > 3 && day < 21) return "th"; // General rule for ordinal numbers
  switch (day % 10) {
    case 1: return "st";
    case 2: return "nd";
    case 3: return "rd";
    default: return "th";
  }
}

function formatStartDate(dateString, locale = "en") {
  if (!dateString) return "";

  const date = parseISO(dateString);

  const selectedLocale = locale === "ar" ? ar : undefined;

  // Format the start date as "MMMM d" (e.g., "December 4" or "ديسمبر 4" in Arabic)
  const monthAndDay = format(date, "MMMM d", { locale: selectedLocale });

  // Add ordinal suffix for English only
  if (locale !== "ar") {
    const day = format(date, "d");
    const suffix = getOrdinalSuffix(parseInt(day, 10));
    return monthAndDay.replace(day, `${day}${suffix}`);
  }

  return monthAndDay;
}

function formatEndDate(dateString, locale = "en") {
  if (!dateString) return "";

  const date = parseISO(dateString);

  const selectedLocale = locale === "ar" ? ar : undefined;

  // Format the end date as "d, yyyy" (e.g., "7, 2024" or "7، 2024" in Arabic)
  const day = format(date, "d", { locale: selectedLocale });

  if (locale === "ar") {
    return `${day}، ${format(date, "yyyy", { locale: selectedLocale })}`;
  }

  const suffix = getOrdinalSuffix(parseInt(day, 10));
  return `${day}${suffix}, ${format(date, "yyyy", { locale: selectedLocale })}`;
}

function formatDate(dateString, locale = "en") {
  if (!dateString) return "";

  const date = parseISO(dateString);

  const selectedLocale = locale === "ar" ? ar : undefined;

  // Format the date to "MMMM d, yyyy" (e.g., "December 4, 2024" or "ديسمبر 4، 2024" in Arabic)
  const formattedDate = format(date, "MMMM d, yyyy", { locale: selectedLocale });

  if (locale === "ar") {
    return formattedDate.replace(",", "،");
  }

  // Add ordinal suffix for English only
  const day = format(date, "d");
  const suffix = getOrdinalSuffix(parseInt(day, 10));

  return formattedDate.replace(day, `${day}${suffix}`);
}

const tournament = (props) => {   
    
   const route = useRouter()
   const pathname = route.pathname
   const yoastData = props?.TournamentData?.yoast_head_json;

  if (!props?.TournamentData) {
    return null;
  }
    return (
        <div>
            {yoastData && <Yoast meta={yoastData} />}
            
      {props &&
        props?.TournamentData &&
        props?.TournamentData?.acf &&
        (props?.TournamentData?.acf?.banner_image || props?.TournamentData?.title || props?.TournamentData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.TournamentData?.acf?.banner_image?.url}
                banner_text={parse(props?.TournamentData?.acf?.banner_title || props?.TournamentData?.title?.rendered)}
            />
            ) : null}
            {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.news_menu &&
        <section className={`${common.tab_section} ${common.mediacenter_tab_section} `}>
          <div className={`${common.container}`}>
            <ul className={`${common.container_tab}`}>
              {props?.IRMenuData?.news_menu &&
                props?.IRMenuData?.news_menu.map((data, irindex) => (
                <li key={irindex}>
                  <Link                      
                      className={`${pathname == data?.learn_menu?.url ? `${common.active_tab}` : ''}`}
                      target={data?.learn_menu?.target}
                      href={data.learn_menu?.url}
                  >
                    {data?.learn_menu?.title && parse(data?.learn_menu?.title)}
                  </Link>
                </li>
              ))}  
            </ul>
          </div>
        </section>
      }
            

            {/* ---upcoming tournaments--- */}
            {props &&
                props?.TournamentData &&
                props?.TournamentData?.acf &&
                props?.upcomingEvents && props?.upcomingEvents?.length > 0 &&
                <section className={`${common.side_line} ${common.tab_side_line}`}>
                    <div
                        className={`${common.container} ${common.upcoming_tournament_container} ${common.pt_50}`}
                    >
                        {props?.TournamentData?.acf?.upcoming_title &&
                            <h3 data-aos="fade-up" data-aos-duration="700">{parse(props?.TournamentData?.acf?.upcoming_title)}</h3>
                        }
                        <ul className={common.tournament_card_sec}>
                            {props?.upcomingEvents &&
                                props?.upcomingEvents?.map((upcominglist, upindex) => (
                                    <li data-aos="fade-up" data-aos-duration="700"
                                        style={{
                                            background:
                                                `url(${upcominglist?._embedded?.["wp:featuredmedia"]?.[0]?.source_url || '/images/news-placeholder.jpg'}) no-repeat center/cover`,
                                        
                                        }}
                                        key={upindex}
                                    >
                                        <div className={common.card_data_bg_img}>
                                            <Image src={upcominglist?.acf?.tournament_mobile?.url || upcominglist?._embedded?.["wp:featuredmedia"]?.[0]?.source_url} width={1300} height={1250} alt=" "/>
                                        </div>
                                        <div className={`${common.card_data_container}  ${upindex % 2==1 ? common.color2 : common.color1}`}>
                                            <div className={`${common.card_box} `}>
                                                {upcominglist?.acf?.tournament_logo &&
                                                    <div className={`${common.card_logo}`} data-aos="fade-up" data-aos-duration="700">
                                                        <Image
                                                            src={upcominglist?.acf?.tournament_logo?.url}
                                                            height={250}
                                                            width={300}
                                                            alt=" "
                                                        />
                                                    </div>
                                                }
                                                {upcominglist?.title &&
                                                    <h4 data-aos="fade-up" data-aos-duration="700">
                                                        {upcominglist?.title && parse(upcominglist?.title?.rendered)}
                                                    </h4>
                                                }
                                                <ul className={`${common.place_list}`}>
                                                    <li data-aos="fade-up" data-aos-duration="700">
                                                        <div className={`${common.list_icon}`}>
                                                            <Image
                                                                src={"/images/calendar-icon.svg"}
                                                                height={30}
                                                                width={30}
                                                                alt=" "
                                                            />
                                                        </div>
                                                        {upcominglist?.acf?.start_date && upcominglist?.acf?.location ? (
                                                            <>
                                                                 {formatStartDate(upcominglist?.acf?.start_date,route.locale)} – {formatEndDate(upcominglist?.acf?.end_date,route.locale) }
                                                            </>
                                                        ) : (
                                                            formatDate(upcominglist?.acf?.start_date,route.locale)
                                                        )}
                                                    </li>
                                                    {upcominglist?.acf?.location &&
                                                        <li data-aos="fade-up" data-aos-duration="700">
                                                            <div
                                                                className={`${common.list_icon} ${common.location} `}
                                                            >
                                                                <Image
                                                                    src={"/images/location-icon.svg"}
                                                                    height={30}
                                                                    width={30}
                                                                    alt=" "
                                                                />
                                                            </div>
                                                            {parse(upcominglist?.acf?.location)}
                                                        </li>
                                                    }
                                                    {upcominglist?.acf?.price &&
                                                        <li data-aos="fade-up" data-aos-duration="700">
                                                            <div className={`${common.list_icon}`}>
                                                                <Image
                                                                    src={"/images/dlr-icon.svg"}
                                                                    height={30}
                                                                    width={30}
                                                                    alt=" "
                                                                />
                                                            </div>
                                                            {parse(upcominglist?.acf?.price)}
                                                        </li>
                                                    }
                                                </ul>
                                                <ul className={`${common.button_list}`}>
                                                    {upcominglist?.acf?.buy_tickets_link &&
                                                        <li data-aos="fade-up" data-aos-duration="700">
                                                            <Link
                                                                href={upcominglist?.acf?.buy_tickets_link?.url}
                                                                target={upcominglist?.acf?.buy_tickets_link?.target}
                                                                className={`${common.btn_link} ${common.btn_link_2}  ${common.card_button} ${common.mt_20}`}
                                                                data-aos="fade-up"
                                                                data-aos-duration="700"
                                                            >
                                                                <label>{upcominglist?.acf?.buy_tickets_link?.title && parse(upcominglist?.acf?.buy_tickets_link?.title)}</label>
                                                                <span>
                                                                    <Image
                                                                        src="/images/btn-img.svg"
                                                                        width={30}
                                                                        height={30}
                                                                        alt="button image"
                                                                    />
                                                                </span>
                                                            </Link>
                                                        </li>
                                                    }
                                                    {upcominglist?.acf?.become_a_volunteer_link &&
                                                        <li data-aos="fade-up" data-aos-duration="700">
                                                            <Link
                                                                href={upcominglist?.acf?.become_a_volunteer_link?.url}
                                                                target={upcominglist?.acf?.become_a_volunteer_link?.target}
                                                                className={`${common.btn_link} ${common.btn_link_2}  ${common.card_button} ${common.mt_20}`}
                                                                data-aos="fade-up"
                                                                data-aos-duration="700"
                                                            >
                                                                <label>{upcominglist?.acf?.become_a_volunteer_link?.title && parse(upcominglist?.acf?.become_a_volunteer_link?.title)}</label>
                                                                <span>
                                                                    <Image
                                                                        src="/images/btn-img.svg"
                                                                        width={30}
                                                                        height={30}
                                                                        alt="button image"
                                                                    />
                                                                </span>
                                                            </Link>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </div>
                                    </li>
                                ))}
                        </ul>
                    </div>
                </section>
            }


            {/* ---Past Tournament---- */}
            {props &&
                props?.TournamentData &&
                props?.TournamentData?.acf &&
                props?.pastEvents && props?.pastEvents?.length > 0 &&

                         <section className={`${common.past_tournament_section}`}>
                             <div className={`${common.container} ${common.past_tournament_container} ${common.pt_70}
                                 ${common.pb_70}`}>
                                 {props?.TournamentData?.acf?.past_title &&
                                 <h3 data-aos="fade-up" data-aos-duration="700" className={`${common.mb_10}`}>
                                     {parse(props?.TournamentData?.acf?.past_title)}</h3>
                                 }
                                 {props?.TournamentData?.acf?.past_sub_text &&
                                 <span data-aos="fade-up"
                                     data-aos-duration="700">{parse(props?.TournamentData?.acf?.past_sub_text)}</span>
                                 }

                                 <ul className={`${common.past_tournament_card_list} ${common.pt_40}`}>
                            {props?.pastEvents &&
                                props?.pastEvents.map((data, pastindex) => (
                                     <li data-aos="fade-up" data-aos-duration="700" key={pastindex}>
                                         <div className={`${common.past_tournament_bg_img}`} >

                                             <div className={`${common.image_block_style} ${common.p_relative} ${common.fill_image_wrapper}`}>
                                                 <Image src={data?.acf?.past_listing_image?.url || "/images/win.jpg"} fill style={{ objectFit: 'cover' }}
                                                 sizes="(max-width: 768px) 100vw"
                                                     alt=" " />

                                                 <Image className={`${common.w_100} ${common.holder}`}
                                                     src={"/images/win.jpg"} width={672} height={447} alt="button image"
                                                     style={{ height: 'auto', width: '100%', display: 'block' }} />
                                             </div>

                                             <div className={`${common.past_card_content}`}>
                                                 <div className={`${common.past_card_logo}`}>
                                                     <DynamicImage src={data?.acf?.tournament_logo?.url} height={250} width={300} alt="" />
                                                     <DynamicImage src={data?.acf?.tournament_white_logo?.url ? data?.acf?.tournament_white_logo?.url : data?.acf?.tournament_logo?.url } height={250} width={300} alt="" />
                                                 </div>

                                                 <p>{data.title && parse(data.title?.rendered)}</p>
                                                {(data.acf?.winning_name || data?.acf?.winning_year) &&
                                                    <div className={`${common.sub_card_content}`}>
                                                        <h5>{data.acf?.winning_name && parse(data.acf?.winning_name)}</h5>
                                                        {data?.acf?.winning_year && <span>{data?.acf?.winning_year}</span>}
                                                    </div>
                                                }
                                             </div>
                                         </div>
                                     </li>
                                     ))}
                                 </ul>
                             </div>
            </section>
            }
           
        </div>
    );
};

export default tournament;




export const getStaticProps = async (locale) => {
// const { getHome } = await usePublicServices();
    const TournamentData = await getTournamentpage(locale); 
    const IRMenuData = await getThemeoptions(locale.locale);     
    const query = "";
    const TournamentPostsData = await getTournamentPosts(locale, query)
    const currentDate = new Date();  
// upcoming events
    const upcomingEvents = TournamentPostsData.filter((event) => {
    const startDate = event?.acf?.start_date ? parseISO(event?.acf?.start_date) : null;
    const endDate = event?.acf?.end_date ? parseISO(event?.acf?.end_date) : null;

    return (      
      (startDate && isAfter(startDate, currentDate)) || //past start_date         
      (startDate && endDate && isAfter(endDate, currentDate)) ||   // start_date and end_date      
        (endDate && isAfter(endDate, currentDate))// only has end_date
        
    );
  });

 // Filter past events
  const pastEvents = TournamentPostsData.filter((event) => {
    const startDate = event?.acf?.start_date ? parseISO(event.acf.start_date) : null;
    const endDate = event?.acf?.end_date ? parseISO(event.acf.end_date) : null;

    return (
      // Event with both start_date and end_date, and end_date is in the past
      (startDate && endDate && isBefore(endDate, currentDate)) ||
      // Event with only a start_date in the past and no end_date
      (startDate && !endDate && isBefore(startDate, currentDate))
    );
  });

//    console.log("Past Events:", pastEvents.id);
//   console.log("Upcoming Events:", upcomingEvents);
  
    
  return {
    props: {
          TournamentData: TournamentData || null,  
          IRMenuData: IRMenuData || null,  
          TournamentPostsData: TournamentPostsData || null,
          upcomingEvents: upcomingEvents || [],
          pastEvents: pastEvents || [],
    },
    revalidate: 10,
  };
};