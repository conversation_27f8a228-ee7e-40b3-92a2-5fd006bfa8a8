import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import Banner from "@/component/banner/Banner";
import DynamicImage from "@/component/DynamicImage";



import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { parseISO, format } from "date-fns";
import { ar } from "date-fns/locale";
import { getNewspage, getThemeoptions,getInsightsPosts } from "@/utils/lib/server/publicServices";
import { useRouter } from 'next/router';


const DateConvert = (datetimeString, locale = "en") => {
  if (!datetimeString || typeof datetimeString !== "string") {
    // console.error("Invalid input for DateConvert:", datetimeString);
    return "Invalid Date";
  }

  try {
    const parsedDate = parseISO(datetimeString);
    const selectedLocale = locale === "ar" ? ar : undefined; // Use Arabic locale if selected, fallback to default
    const formattedDate = format(parsedDate, "dd MMMM yyyy", {
      locale: selectedLocale,
    });
    return formattedDate;
  } catch (error) {
    // console.error("Error parsing or formatting date:", error);
    return "Invalid Date";
  }
};

const becomeMember = (props) => {
  const route = useRouter()
  const pathname = route.pathname
  const yoastData = props?.NewsData?.yoast_head_json;

  const [visiblePosts, setVisiblePosts] = useState(10); // Initial visible posts

  const loadMorePosts = () => {
    setVisiblePosts((prev) => prev + 6); // Load 5 more posts
  };

  if (!props?.NewsData) {
    return null;
  }


  return (
    <div>
      {yoastData && <Yoast meta={yoastData} />}
            
      {props &&
        props?.NewsData &&
        props?.NewsData?.acf &&
        (props?.NewsData?.acf?.banner_image || props?.NewsData?.title || props?.NewsData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.NewsData?.acf?.banner_image?.url}
                banner_text={parse(props?.NewsData?.acf?.banner_title || props?.NewsData?.title?.rendered)}
            />
      ) : null}
      
{props &&
        props?.IRMenuData &&
        props?.IRMenuData?.news_menu &&
        <section className={`${common.tab_section} ${common.mediacenter_tab_section} `}>
          <div className={`${common.container}`}>
            <ul className={`${common.container_tab}`}>
              {props?.IRMenuData?.news_menu &&
                props?.IRMenuData?.news_menu.map((data, irindex) => (
                <li key={irindex}>
                  <Link                      
                      className={`${pathname == data?.learn_menu?.url ? `${common.active_tab}` : ''}`}
                      target={data?.learn_menu?.target}
                      href={data.learn_menu?.url}
                  >
                    {data?.learn_menu?.title && parse(data?.learn_menu?.title)}
                  </Link>
                </li>
              ))}  
            </ul>
          </div>
        </section>
      }

      
      <section
        className={`${common.d_flex} ${common.tab_side_line} ${common.side_line} ${common.pt_100} ${common.pb_100} ${common.mobile_padding_tb}  `}
      >
        <div className={`${common.container}`}>
          {props?.InsightsPostsData?.[0] && (
          <div className={`${common.d_flex_wrap} ${common.feacherd_news}`}>
            <div
              className={`${common.w_50} ${common.p_relative} ${common.media_item_image}`}
              data-aos="fade-up"
              data-aos-duration="700"
            >
                <Link
                  href={props?.InsightsPostsData[0]?.slug ? `/news/${props?.InsightsPostsData[0]?.slug}` : "#" }
                className={`${common.fill_image_wrapper} ${common.link_d_block}`}
                >
                <Image
                  src={props?.InsightsPostsData[0]?._embedded?.["wp:featuredmedia"]?.[0]?.source_url || '/images/news-placeholder.jpg'}
                  fill
                  style={{ objectFit: "cover" }}
                  alt=" "
                   sizes="(max-width: 768px) 100vw"
                />
                <Image
                  className={`${common.w_100} ${common.holder}`}
                  src="/images/news-holder.jpg"
                  width={606}
                  height={664}
                  alt="button image"
                  style={{ height: "auto", width: "100%", display: "block" }}
                />
              </Link>
            </div>
            <div
              className={`${common.media_item_content} ${common.w_50}`}
              data-aos="fade-up"
              data-aos-duration="700"
              >
                {props?.InsightsPostsData[0]?.date &&
              <span className={`${common.date_lable}`}>
                  {DateConvert(props?.InsightsPostsData[0]?.date, route.locale)}</span>
                }
                {props?.InsightsPostsData[0]?.title &&
                  <h5>
                    {props?.InsightsPostsData[0]?.title &&
                      parse(props?.InsightsPostsData[0]?.title?.rendered)}
                  </h5>
                }
                {props?.InsightsPostsData[0]?.content &&
                  <div>
                    {props?.InsightsPostsData[0]?.content &&
                      (() => {
                        const content = props?.InsightsPostsData[0]?.content?.rendered || '';
                        const slicedContent = content.split(' ').slice(0, 80).join(' '); // Get the first 100 words
                        return parse(slicedContent); // Use `parse` to render the sliced content
                      })()}
                  </div>
                }
                
              <Link
                href={props?.InsightsPostsData[0]?.slug ? `/news/${props?.InsightsPostsData[0]?.slug}` : "#" }
                className={`${common.btn_read} ${common.mt_80}  ${common.trans}`}
              >
                {route.locale === "en" ? "Read more" : "اقرأ المزيد"} <DynamicImage src={"/images/read.svg"} />
              </Link>
            </div>
          </div>
          )}
          {props?.InsightsPostsData &&
            <ul className={`${common.news_list} ${common.mt_80}   `}>
            
              {props?.InsightsPostsData &&
                props?.InsightsPostsData?.slice(1, visiblePosts).map((newslist, nindex) => (
                  <li key={nindex} data-aos="fade-up" data-aos-duration="700">
                    <div className={`${common.news_list_main_block}`}>
                      <div className={` ${common.p_relative} ${common.media_item_image}`}  >
                        <Link href={newslist.slug ? `/news/${newslist.slug}` : "#"}
                        className={`${common.fill_image_wrapper} ${common.link_d_block}`}>
                          <Image
                            src={newslist?._embedded?.["wp:featuredmedia"]?.[0]?.source_url || '/images/news-placeholder.jpg'}
                            fill
                            style={{ objectFit: "cover" }}
                            alt=" "
                            sizes="(max-width: 768px) 100vw"
                          />
                          <Image
                            className={`${common.w_100} ${common.holder}`}
                            src="/images/news_list_holder.jpg"
                            width={606}
                            height={664}
                            alt="button image"
                            style={{
                              height: "auto",
                              width: "100%",
                              display: "block",
                            }}
                          />
                        </Link>
                      </div>
                      <div className={`${common.media_item_content} `} >
                        {newslist.date &&
                          <span className={`${common.date_lable}`}>{DateConvert(newslist.date, route.locale)}</span>
                        }
                        <Link href={newslist.slug ? `/news/${newslist.slug}` : "#"}>
                          {newslist?.title &&
                            <h5>
                              {newslist?.title && parse(newslist?.title?.rendered)}
                            </h5>
                          }
                        </Link>

                        <Link
                          href={newslist.slug ? `/news/${newslist.slug}` : "#"}
                          className={`${common.btn_read} ${common.mt_30}  ${common.trans}`}
                        >
                          {route.locale === "en" ? "Read more" : "اقرأ المزيد"} <DynamicImage src={"/images/read.svg"} />
                        </Link>
                      </div>
                    </div>
                  </li>
                ))}
            </ul>
          }

           {visiblePosts < props?.InsightsPostsData?.length && (
            <a onClick={loadMorePosts}
              className={`${common.btn_read} ${common.mt_80} ${common.trans} ${common.load_more} `}
            >
              {route.locale === "en" ? "Load More..." : "تحميل المزيد ..."}
            </a>
            )}
          
        </div>
      </section>
    </div>
  );
};

export default becomeMember;



export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();

  const NewsData = await getNewspage(locale);  
  const IRMenuData = await getThemeoptions(locale.locale);
  const query = "";
  const InsightsPostsData = await getInsightsPosts(locale.locale, query); 
  
  //console.log('data',InsightsPostsData)
  return {
    props: {
      NewsData: NewsData || null,    
      IRMenuData: IRMenuData || null,
      InsightsPostsData: InsightsPostsData || null,
    },
    revalidate: 10,
  };
};