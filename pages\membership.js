import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import style from "@/styles/membership.module.scss";
import Banner from "@/component/banner/Banner";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import Sheet from "@mui/joy/Sheet";

import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import { getMembership,getMembershipsPosts } from "@/utils/lib/server/publicServices";

const membership = (props) => {

  const [open, setOpen] = useState(false);
 const [modalData, setModalData] = useState(null);

 const handleOpen = (index, section) => {
    let selectedData = null;
    if (section === "joint") {
      selectedData = props?.joint_membershipsRelation[index];
    } else if (section === "premium") {
      selectedData = props?.PremiumRelation[index];
    } else if (section === "midweek") {
      selectedData = props?.MidweekRelation[index];
    }
    setModalData(selectedData);
    setOpen(true);
  };

  const [activeTab, setActiveTab] = useState("PremiumJoint"); // Default active tab

  // const handleScroll = (event, targetId) => {
  //   event.preventDefault(); // Prevent default action
  //   const targetElement = document.getElementById(targetId);
  //   if (targetElement) {
  //     targetElement.scrollIntoView({ behavior: "smooth" });
  //   }
  //   setActiveTab(targetId);
  // };
  const handleScroll = (event, targetId) => {
    event.preventDefault(); // Prevent default action
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      const offset = 80; // Offset in pixels
      const elementPosition =
        targetElement.getBoundingClientRect().top + window.scrollY;
      const offsetPosition = elementPosition - offset;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
    setActiveTab(targetId); // Set active tab
  };

   const yoastData = props?.MembershipData?.yoast_head_json;

  if (!props?.MembershipData) {
    return null;
  }


  return (
    <div>

      <Modal
          aria-labelledby="modal-title"
          aria-describedby="modal-desc"
          open={open}
          onClose={() => setOpen(null)}
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Sheet variant="outlined" className={common.modal_container}>
            <ModalClose variant="plain" className={`${common.modal_close} `} />
          <div className={common.modal_inside}>
          <div className={`${common.modal_discount_sec} `}>
            {modalData?.acf.price && modalData?.acf.price_prefix &&
              <span>
                {modalData?.acf.price} {modalData?.acf.price_prefix}
              </span>
            }
            {modalData?.acf.price_discount_text &&
              <p>
                {modalData?.acf.price_discount_text}
              </p>
            }
            </div>
            <h4>{modalData?.title && parse(modalData?.title?.rendered)}</h4>
          <div className={`${common.modal_content_group} `}>
          {modalData?.content && parse(modalData?.content?.rendered)}
          </div>
          </div>
          </Sheet>
      </Modal>
      

      {yoastData && <Yoast meta={yoastData} />}
            
      {props &&
        props?.MembershipData &&
        props?.MembershipData?.acf &&
        (props?.MembershipData?.acf?.banner_image || props?.MembershipData?.title || props?.MembershipData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.MembershipData?.acf?.banner_image?.url}
                banner_text={parse(props?.MembershipData?.acf?.banner_title || props?.MembershipData?.title?.rendered)}
            />
      ) : null}

      {props &&
        props?.MembershipData &&
        props?.MembershipData?.acf &&
        (props?.MembershipData?.acf?.memberships_title || props?.MembershipData?.acf?.memberships_content || props?.MembershipData?.acf?.memberships_buttons) ? ( 
      
       <section className={` ${common.side_line} `}>
        <div className={`${common.container} ${style.membership_container}`}>
          <div className={` ${style.text_block}`}>
             {props?.MembershipData?.acf?.memberships_title &&
                <h4  data-aos="fade-up" data-aos-duration="700">{parse(props?.MembershipData?.acf?.memberships_title)}</h4>
            }
            {props?.MembershipData?.acf?.memberships_content &&
            <div data-aos="fade-up" data-aos-duration="700">
                    {parse(props?.MembershipData?.acf?.memberships_content)}
                    </div>
            }

                {props?.MembershipData?.acf?.memberships_buttons &&
                  <Link data-aos="fade-up" data-aos-duration="700"
                    href={props?.MembershipData?.acf?.memberships_buttons?.url}
                    target={props?.MembershipData?.acf?.memberships_buttons?.target}
                    className={`${common.btn_link} ${style.btn_link_01} ${style.mt_30} ${common.btn_link_2} `}
                  >
                    <label>{props?.MembershipData?.acf?.memberships_buttons?.title && parse(props?.MembershipData?.acf?.memberships_buttons?.title)}</label>
                    <span>
                      <Image
                        src={"/images/btn-img.svg"}
                        height={30}
                        width={30}
                        alt="image"
                    />                    
                    </span>
                  </Link>
                }
          </div>
        </div>
      </section>
      ) : null}
      
    <section className={style.section_scrollbar} data-aos="fade-up" data-aos-duration="700">
        {/* ==Explore our Memberships Tabs== */}
        <div className={`${style.explore_membership_sec} `}>
          <div
            className={`${style.explore_membership_head} ${common.container} `}
          >
            {props?.MembershipData?.acf?.explore_title &&
                <h4>{parse(props?.MembershipData?.acf?.explore_title)}</h4>
            }

           
            <ul className={`${style.explore_membership_tabs} `} id="scrollTab">
              {props?.MembershipData?.acf?.explore_navigation &&
                props?.MembershipData?.acf?.explore_navigation.map((explore, exindex) => (
                  <li key={exindex}>
                    <div
                      className={`${style.tab_link} ${activeTab === explore?.section_id ? style.active : ""
                        }`}
                      onClick={(e) => handleScroll(e, explore?.section_id)}
                    >
                      {explore.section_name && parse(explore.section_name)}
                    </div>
                  </li>
                ))}
             
            </ul>
          </div>
        </div>
         {/* Joint Premium Memberships */}
{props &&
          props?.MembershipData &&
          props?.MembershipData?.acf &&
          props?.MembershipData?.acf?.premium_memberships?.length > 0 && (
       
        <div className={common.swiper_card_section} id="PremiumJoint">
          <div
            className={`${common.container} ${common.swiper_card_container}`}
          >
           {props?.MembershipData?.acf?.joint_title &&
                <h4 data-aos="fade-up" data-aos-duration="700">{parse(props?.MembershipData?.acf?.joint_title)}</h4>
            }

            <ul className={common.card_group_sec}>
              {props?.joint_membershipsRelation && props?.joint_membershipsRelation.map((data, index) => (
                <li key={index} 
                  data-aos="fade-up" data-aos-duration="700"
                  className={common.card_box}
                  // onClick={() => handleOpen(index, "joint")}
                >
                  <div className={common.card_img}>
                    <div
                      className={`${common.image_block_style} ${common.card_image_height} ${common.mask_after} ${common.fill_image_wrapper}`}
                    >
                      <Image
                        src={data?.acf?.membership_image?.url}
                        alt="image"
                        fill
                        style={{ objectFit: "cover" }}
                         sizes="(max-width: 768px) 100vw"
                      />

                      <Image
                        className={`${common.w_100} ${common.holder}`}
                        src="/images/holder_4.jpg"
                        width={672}
                        height={447}
                        alt="button image"
                        style={{ height: 'auto', width: '100%', display: 'block' }}
                      />
                    </div>
                    <div className={common.card_content}>
                      <h5>{data?.title && parse(data?.title?.rendered)}</h5>
                      <div className={common.card_count}>
                         {data?.acf?.price && parse(data?.acf?.price)}
                            { data?.acf?.price && data?.acf?.price_prefix &&
                              <span>{parse(data?.acf?.price_prefix)}</span>
                            }
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
)}
        {/* ==Premium Memberships== */}
        {props &&
          props?.MembershipData &&
          props?.MembershipData?.acf &&
          props?.MembershipData?.acf?.premium_memberships?.length > 0 && (
        <div
          className={`${common.swiper_card_section} ${common.side_line}  `}
          style={{ background: "white", overflow: "hidden" }}
          id="Premium"
        >
          <div
            className={`${common.container} ${common.swiper_card_container}`}
          >
            {props?.MembershipData?.acf?.premium_title &&
                <h4 data-aos="fade-up" data-aos-duration="700">{parse(props?.MembershipData?.acf?.premium_title)}</h4>
            }

            <ul className={`${common.card_group_sec} ${common.premium_sec}`}>
              {props?.PremiumRelation && props?.PremiumRelation.map((data, index) => (
                <li data-aos="fade-up" data-aos-duration="700"
                  className={common.card_box}
                  // onClick={() => handleOpen(index, "premium")}
                  key={index}
                >
                  <div className={common.card_img}>
                    <div
                      className={`${common.image_block_style} ${common.card_image_height} ${common.mask_after} ${common.fill_image_wrapper}`}
                    >
                      <Image
                        src={data?.acf?.membership_image?.url}
                        alt="image"
                        fill
                        style={{ objectFit: "cover" }}
                         sizes="(max-width: 768px) 100vw"
                      />
                      <Image
                        className={`${common.w_100} ${common.holder}`}
                        src="/images/holder_4_new.jpg"
                        width={672}
                        height={447}
                        alt="button image"
                        style={{ height: 'auto', width: '100%', display: 'block' }}
                      />
                    </div>
                    <div className={common.card_content}>
                      <h5>{data?.title && parse(data?.title?.rendered)}</h5>
                      <div className={common.card_count}>
                          {data?.acf?.price && parse(data?.acf?.price)}
                            { data?.acf?.price && data?.acf?.price_prefix &&
                              <span>{parse(data?.acf?.price_prefix)}</span>
                            }
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
          )}
        
        {/* ==midweek== */}
        {props &&
          props?.MembershipData &&
          props?.MembershipData?.acf &&
          props?.MembershipData?.acf?.midweek_memberships?.length > 0 && (
            <div className={common.swiper_card_section} id="Midweek">
              <div
                className={`${common.container} ${common.swiper_card_container}`}
              >
                {props?.MembershipData?.acf?.midweek_title &&
                  <h4 data-aos="fade-up" data-aos-duration="700">{parse(props?.MembershipData?.acf?.midweek_title)}</h4>
                }
                <ul className={common.card_group_sec}>
                  {props?.MidweekRelation && props?.MidweekRelation.map((data, index) => (
                    <li data-aos="fade-up" data-aos-duration="700"
                      className={common.card_box}
                      // onClick={() => handleOpen(index, "midweek")}
                      key={index}
                    >
                      <div className={common.card_img}>
                        <div
                          className={`${common.image_block_style} ${common.card_image_height}  ${common.mask_after} ${common.fill_image_wrapper}`}
                        >
                          <Image
                            src={data?.acf?.membership_image?.url}
                            alt="image"
                            fill
                            style={{ objectFit: "cover" }}
                             sizes="(max-width: 768px) 100vw"
                          />
                          <Image
                            className={`${common.w_100} ${common.holder}`}
                            src="/images/holder_6.jpg"
                            width={672}
                            height={447}
                            alt="button image"
                            style={{ height: 'auto', width: '100%', display: 'block' }}
                          />
                        </div>
                        <div className={common.card_content}>
                          <h5>{data?.title && parse(data?.title?.rendered)}</h5>
                          <div className={common.card_count}>
                            {data?.acf?.price && parse(data?.acf?.price)}
                            { data?.acf?.price && data?.acf?.price_prefix &&
                              <span>{parse(data?.acf?.price_prefix)}</span>
                            }
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
      </section>
      <   hr />
    </div>



  );
};

export default membership;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const MembershipData = await getMembership(locale); 
   const query = "";
   const joint_membershipsPostsData = await getMembershipsPosts(locale, query);
  
  // Joint Premium Memberships Section  
  
      let joint_membershipsPosts = [];
      if (MembershipData && MembershipData?.acf && Array.isArray(MembershipData?.acf?.joint_memberships)) {
      joint_membershipsPosts = MembershipData?.acf?.joint_memberships;
      }

      // Format Investors Resources  for use in the component
      let joint_membershipsRelation = [];
      if (joint_membershipsPosts.length > 0) {
      joint_membershipsRelation = joint_membershipsPosts.map((id) =>
        joint_membershipsPostsData?.find((post) => post.id === id)
      ).filter(Boolean); // To ensure undefined values (if any) are removed
      }
        
  // Premium Memberships Section    
      let PremiumPosts = [];
      if (MembershipData && MembershipData?.acf && Array.isArray(MembershipData?.acf?.premium_memberships)) {
      PremiumPosts = MembershipData?.acf?.premium_memberships;
      }

      // Format Investors Resources  for use in the component 
      let PremiumRelation = [];
      if (PremiumPosts.length > 0) {
      PremiumRelation = PremiumPosts.map((id) =>
        joint_membershipsPostsData?.find((post) => post.id === id)
      ).filter(Boolean); // To ensure undefined values (if any) are removed
      }
      
   // Midweek Memberships Section    
      let MidweekPosts = [];
      if (MembershipData && MembershipData?.acf && Array.isArray(MembershipData?.acf?.midweek_memberships)) {
      MidweekPosts = MembershipData?.acf?.midweek_memberships;
      }

      // Format Investors Resources  for use in the component     
      let MidweekRelation = [];
      if (MidweekPosts.length > 0) {
      MidweekRelation = MidweekPosts.map((id) =>
        joint_membershipsPostsData?.find((post) => post.id === id)
      ).filter(Boolean); // To ensure undefined values (if any) are removed
      }

      
      //console.log('homeData', joint_membershipsRelation)
 
  return {
    props: {
      MembershipData: MembershipData || null,      
      joint_membershipsRelation: joint_membershipsRelation || null,      
      PremiumRelation: PremiumRelation || null,      
      MidweekRelation: MidweekRelation || null,      
    },
    revalidate: 10,
  };
};