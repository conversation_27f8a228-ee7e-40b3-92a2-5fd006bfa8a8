// @import "variable", "base";
@import '/styles/variable', '/styles/base';

.banner_section {
    width: 100%;



    .banner_background {
        height: auto;
        max-height: 750px;
        width: 100%;
        position: relative;
        display: flex;
        align-items: flex-end;
        justify-content: center;

        img {
            height: 100%;
            object-fit: cover;
            width: 100%;
            display: block;

        }

        >h2 {
            color: #ffffff;
            text-align: center;
            // font-family: "AdelleSans-Bold", sans-serif;
            font-size: 3.438rem;
            line-height: 121%;
            font-weight: 700;
            position: absolute;
            bottom: 5%;

            @media #{$media-1440} {

                // font-size: 3.3rem;

            }

            @media #{$media-1280} {

                margin-bottom: 15px;
                font-size: 4rem;
                padding-left: 2%;
                padding-right: 2%;
            }

            @media #{$media-480} {

                margin-bottom: 5px;
            }
        }



        @media #{$media-700} {
             height: 250px;
        }
    }
}