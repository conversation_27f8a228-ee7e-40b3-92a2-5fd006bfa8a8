@import "variable", "base";

.overflow_hidden {
  overflow: hidden;
}

.white_border_btn {
  display: inline-flex;
  position: relative;
  padding: 15px 44px;
  height: 50px;
  align-items: center;
  justify-content: center;
  border: 1px solid #fff;
  border-radius: 50px;
  color: #fff;
  overflow: hidden;

  span {
    position: relative;
    z-index: 100;
    transform: translateX(0px);
    transition: transform 0.02s ease-in-out;
  }

  &:after {
    display: block;
    content: "";
    width: 0%;
    height: 100%;
    background: #fff;
    position: absolute;
    left: 0;
    transition: all 0.4s ease-out 0s;
    -moz-transition: all 0.4s ease-out 0s;
    -webkit-transition: all 0.4s ease-out 0s;
    -o-transition: all 0.4s ease-out 0s;
    z-index: 50;
  }

  &:hover {
    color: #000000;

    &:after {
      width: 100%;
    }
  }

  @media #{$media-1200} {
    height: 40px;
    padding-top: 5px;
    padding-bottom: 5px;
  }

  @media #{$media-700} {
    height: 35px;
  }
}

.lang_txt {
  display: inline-block;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: #fff;
  font-family: "AktivGrotesk";
  margin-top: -5px;
  padding-left: 15px;
  padding-right: 15px;

  &.active {
    position: relative;
    z-index: 50;
  }

  @media #{$media-500} {
    padding: 0 10px;
    font-size: 14px;
    line-height: 22px;
  }
}

.lang_txt:hover {
  color: #805e59;
}

.btn_link {
  display: inline-flex;
  height: 48px;
  align-items: center;
  border-radius: 50px;
  background: #805e59;
  padding-left: 25px;
  padding-right: 7px;
  gap: 40px;
  color: #f5ede6;
  border: 1px solid transparent;
  position: relative;
  z-index: 2;
  overflow: hidden;
  padding-right: 55px;
  justify-content: center;
  cursor: pointer;

  span {
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: rgba(245, 237, 230, 0.2);
    padding-left: 5px;
    transition: var(--transition);
    position: relative;
    z-index: 4;
    position: absolute;
    right: 6px;

    @media #{$media-1440} {
      right: 2px;
    }

    img {
      width: 11px;
      height: auto;

      @media #{$media-700} {
        width: 11px;
      }
    }



    @media #{$media-1440} {
      width: 35px;
      height: 35px;
    }

    @media #{$media-700} {
      width: 33px;
      height: 33px;
    }
  }

  label {
    position: relative;
    z-index: 4;
    cursor: pointer;
  }

  &:after {
    transition: all 0.4s ease-out 0s;
    -moz-transition: all 0.4s ease-out 0s;
    -webkit-transition: all 0.4s ease-out 0s;
    -o-transition: all 0.4s ease-out 0s;
    display: block;
    content: "";
    position: absolute;
    z-index: 3;
    left: 0;
    width: 0;
    height: 100%;
    background: #744e48;
  }

  &:hover {
    color: #fff;

    &:after {
      width: 100%;
    }
  }

  @media #{$media-1440} {
    height: 40px;
  }

  @media #{$media-700} {
    height: 38px;
  }
}

.btn_link:disabled {
  cursor: no-drop;

  label {
    cursor: no-drop;
  }
}

.container {
  width: 100%;
  max-width: calc(1600px + 6%);
  padding-left: 3%;
  padding-right: 3%;
  margin: 0 auto;
  position: relative;
  z-index: 3;

  @media #{$media-1200} {
    padding-left: 5%;
    padding-right: 5%;
  }
}

.arrowBtn {
  .button_icon {
    border-radius: 50%;
    background-color: hwb(28 90% 4% /20%);
    height: 38px;
    width: 38px;
    display: flex;
    align-items: center;
    justify-content: center;

    >span {
      display: block;
      width: 30%;
      margin-inline-start: 5px;
      position: relative;

      img {
        display: block;
        width: 100%;
        object-fit: contain;
      }

      &::after {
        position: absolute;
        content: "";
        opacity: 0;
        height: 100%;
        width: 100%;
        background-image: url("/images/btn-img.png");
        background-repeat: no-repeat;
        background-size: contain;
        top: 0;
        left: -5px;
        transition: 0.3s ease;
      }
    }
  }

  &:hover span::after {
    opacity: 1;
    left: 0;
  }
}

.title_h1 {

  h2,
  h3 {
    font-size: 3.125rem;
    line-height: 112%;
    font-weight: 600;
    color: #241f21;
  }
}

.welcome_txt_block {
  max-width: 1240px;
  margin-left: auto;
  margin-right: auto;

  .text_center {
    @media #{$media-700} {
      text-align: start;
    }
  }
}

.white_txt {

  h1,
  h3,
  h2,
  p {
    color: #fff;
  }
}

.service_ul_new {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
}

.service_ul_new li {
  list-style: none;
  height: 493px;
  flex: 1 1 16.6%;
  filter: brightness(1);
  /* Full brightness */
  transition: flex 0.5s ease, filter 0.3s ease;
  /* Default width as 16.6% */

  position: relative;
  background-position: center center !important;
  background-size: auto 100% !important;
  transition: flex 0.3s ease-in-out, background-size 0.5s ease-in-out;
  width: 100%;

  cursor: pointer;

  .service_image {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
  }

  @media #{$media-1600} {
    height: 400px;
  }

}

.service_ul_new li .text {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding-bottom: 30px;
}

.service_ul_new li .text h3 {
  font-size: 1.5rem;
  color: #fff;
  text-align: center;
  margin: 0;
}

/* On hover, increase the hovered li's width to 25% */
.service_ul_new li:hover {
  flex: 1 1 25%;
  background-size: auto 120% !important;
  filter: brightness(1);
  /* Full brightness */
  transition: flex 0.5s ease, background-size 0.3s ease, filter 0.3s ease;
}

/* Adjust the non-hovered li to share the remaining space */
.service_ul_new:hover li:not(:hover) {
  flex: 1 1 calc((100% - 25%) / 5);
  /* Adjust width for remaining items */
  filter: brightness(0.5);
  /* Makes the non-hovered items darker */
  transition: flex 0.5s ease, filter 0.3s ease;
}

.fill_image_wrapper {
  width: 100%;
  height: 100%;
  position: relative;

  &.link_d_block {
    display: block;
  }
}


.hover-txt {
  text-align: center;
  height: 0;
  overflow: hidden;
  padding-right: 20px;
  padding-left: 20px;
}

.hover-txt p {
  color: #fff;
}

.service_ul_new li:hover .hover-txt {
  height: 50px;
  transition-delay: 6s;
  transition: height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.word_class_section {
  padding: 250px 0;

  .word_class_container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    .text_block {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      >h4 {
        color: #f5ede6;
        text-align: center;
        font-family: "AdelleSans-Bold", sans-serif;
        font-size: 3.125rem;
        line-height: 112%;
        font-weight: 700;
      }

      .count_block {
        display: flex;
        gap: 10px;

        li {
          background: rgb(128, 94, 89, 0.8);
          border-radius: 10px;
          padding: 7px 20px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          @media #{$media-700} {
            padding: 7px 15px;
          }

          >span {
            color: #ffffff;
            text-align: center;
            font-size: 3.438rem;
            line-height: 100%;
            font-weight: 100;
          }

          >p {
            color: #ffffff;
            text-align: center;
            font-size: 16px;
            line-height: 150%;
            font-weight: 100;
            margin-bottom: 0;
            text-transform: capitalize;
          }
        }
      }

      .count_block_top {
        >li {
          width: 50%;
        }
      }

      .color_block {
        margin-top: 10px;

        li {
          min-width: 120px;
          padding-block: 20px;
          background-color: var(--color);

          @media #{$media-700} {
            min-width: 80px;
            padding-block: 15px;
          }

          @media #{$media-400} {
            min-width: 70px;

          }

          span {
            font-size: 1.5rem;

            @media #{$media-700} {
              font-size: 2rem;
            }
          }
        }

        li.black_text {
          * {
            color: #000000;
          }
        }

      }

      .button_block {
        margin-top: 35px;
        display: flex;
        flex-wrap: wrap;
        gap: 15px;

        li {
          // a {
          //   display: flex;
          //   align-items: center;
          //   justify-content: center;
          //   gap: 10px;
          //   padding: 5px 7px 5px 35px;
          //   border: 0.5px solid rgba(255, 255, 255, 0.5);
          //   background: hwb(0 100% 0% / 0.24);
          //   backdrop-filter: blur(9.65px);
          //   border-radius: 30px;
          //   color: #f5ede6;
          //   text-align: center;
          //   font-size: 16px;
          //   line-height: 100%;
          //   font-weight: 300;

          //   &:hover {
          //     border: 0.5px solid #805e59;

          //     background: #805e59;

          //   }

          //   @media #{$media-700} {
          //     max-width: 250px; margin-left: auto; margin-right: auto;
          //   }

          // }

          @media #{$media-700} {
            width: 100%;
            text-align: center;
          }

          a {
            @media #{$media-700} {
              min-width: 220px;
            }
          }
        }
      }

      // .text_center{
      //   @media #{$media-700} {
      //     text-align: start;
      //   }
      // }
    }
  }

  @media #{$media-820} {
    padding: 200px 0;
  }
}

.side_line {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    background-image: url("/images/bar1.png");
    background-repeat: repeat-y;
    background-position: right top;
    // background-repeat: no-repeat;
    background-size: 100%;
    right: 0px;
    height: calc(100vh + 120px);
    width: 45px;
    top: -65px;
    z-index: 1;

    @media #{$media-700} {
      height: calc(100vh + 40px);
      width: 45px;
      top: -40px;
    }

    @media #{$media-480} {
      height: calc(100vh + 20px);
      width: 25px;
      top: -20px;
    }

  }

  &.tab_side_line {
    &::before {
      top: -150px;

      @media #{$media-1440} {
        top: -130px;
      }

      @media #{$media-700} {
        top: -115px;
      }

      @media #{$media-480} {
        top: -70px;
      }
    }
  }

  &.banner_side_line {
    &::before {
      top: -30px;

      @media #{$media-1440} {
        top: -30px;
      }

      @media #{$media-700} {
        top: -25px;
      }

      @media #{$media-480} {
        top: -16px;
      }
    }
  }

  &::after {
    content: "";
    position: absolute;
    background-image: url("/images/bar-1.png");
    background-repeat: repeat-y;
    // background-repeat: no-repeat;
    background-size: 100%;
    background-position: left top;
    z-index: 1;
    left: 0px;
    height: calc(100vh + 120px);
    width: 45px;
    top: -65px;

    @media #{$media-700} {
      height: calc(100vh + 40px);
      width: 45px;
      top: -40px;
      display: none;
    }

    @media #{$media-480} {
      height: calc(100vh + 20px);
      width: 25px;
      top: -20px;
    }
  }

  &.tab_side_line {
    &::after {
      top: -150px;

      @media #{$media-1440} {
        top: -130px;
      }

      @media #{$media-700} {
        top: -115px;
      }

      @media #{$media-480} {
        top: -70px;
      }
    }
  }

  &.banner_side_line {
    &::after {
      top: -30px;

      @media #{$media-1440} {
        top: -30px;
      }

      @media #{$media-700} {
        top: -25px;
      }

      @media #{$media-480} {
        top: -16px;
      }
    }
  }
}

.custom_btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 5px 7px 5px 35px;
  border: 0.5px solid rgba(255, 255, 255, 0.5);
  background: hwb(0 100% 0% / 0.24);
  backdrop-filter: blur(9.65px);
  border-radius: 30px;
  color: #f5ede6;
  text-align: center;
  font-size: 16px;
  line-height: 100%;
  font-weight: 300;

  &:hover {
    border: 0.5px solid #805e59;

    background: #805e59;
  }
}

.section_animation_03 {
  width: 100%;
  padding-left: 5px;
  padding-right: 5px;
  margin-bottom: 25px;
  position: relative;
  z-index: 300;
  display: flex;
  flex-wrap: wrap;

  @media #{$media-700} {
    padding-left: 0px;
    padding-right: 0px;
    margin-bottom: 0;
  }

  li {
    padding-left: 10px;
    padding-right: 10px;
    width: 33%;
    position: relative;

    @media #{$media-700} {
      padding-left: 0px;
      padding-right: 0px;
    }

    .play_icon {
      width: 55px;
      height: auto;
      position: absolute;
      top: -27px;
      left: 50%;
      z-index: 500;
      transform: translateX(-50%);
    }

    .play_icon img {
      height: auto;
      width: 100%;
      display: block;
      object-fit: contain;
    }

    h3 {
      font-size: 2.5rem;
      line-height: 2.813rem;
      width: 100%;
      text-align: center;
      margin: 0px;
      margin-bottom: 20px;
      color: #805e59;
      font-weight: 400;
      transition: all 0.4s ease-out 0s;
      -moz-transition: all 0.4s ease-out 0s;
      -webkit-transition: all 0.4s ease-out 0s;
      -o-transition: all 0.4s ease-out 0s;
      font-weight: 700;

      @media #{$media-1600} {
        font-size: 2rem;
        line-height: 2.313rem;
        margin-bottom: 15px;
      }
    }

    p {
      margin: 0px;
      max-width: 409px;
      color: #000;
      margin: 0 auto;
      text-align: center;
      transition: all 0.4s ease-out 0s;
      -moz-transition: all 0.4s ease-out 0s;
      -webkit-transition: all 0.4s ease-out 0s;
      -o-transition: all 0.4s ease-out 0s;

      @media #{$media-1600} {
        font-size: 14px;
        line-height: 23px;
      }
    }

    .full_slide_content {
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.6);
      border: solid 1px #fff;
      border-radius: 15px;
      display: flex;
      padding: 40px 30px 40px;
      align-items: flex-start;
      justify-content: flex-start;
      flex-direction: column;
      transition: all 0.4s ease-out 0s;
      -moz-transition: all 0.4s ease-out 0s;
      -webkit-transition: all 0.4s ease-out 0s;
      -o-transition: all 0.4s ease-out 0s;
      backdrop-filter: blur(0.3125rem);

      &:hover {
        background: rgba(128, 94, 89, 1) !important;
        border-color: rgba(128, 94, 89, 1) !important;

        h3 {
          color: #fff;
        }

        p {
          color: #fff;
        }
      }

      @media #{$media-768} {
        backdrop-filter: blur(10px);
      }
    }

    // @media #{$media-1024} {
    //   height: 15vh;
    // }

    @media #{$media-820} {
      width: 100%;
      margin-bottom: 50px;
    }
  }
}

.h_100vh {
  height: 100vh;

  @media #{$media-820} {
    height: auto;
  }
}

.bg_mask {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 31;
  width: 100%;
  height: 100%;
  background-color: #F5EDE6;
  /* Adjust color as needed */
  pointer-events: none;
  opacity: 0.7;
  transition: all 0.3s ease-in-out;
}

.corporate_golf_inner {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  z-index: 1;
  position: relative;
  height: 100%;
}

.corporate_content_box {
  // max-width: 565px;
  // padding-left: 5%;
  width: 34%;

  @media #{$media-820} {
    width: 100% !important;
  }
}

.corporate_slider_box {
  width: 45%;

  @media #{$media-820} {
    width: 100%;
    margin-top: 40px;
  }
}

.mobile_padding_tb {
  @media #{$media-700} {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}

.corporate_images {
  display: flex;
  flex-wrap: wrap;
  margin: 0px;
  padding: 0px;
  list-style: none;
  row-gap: 25px;
  justify-content: space-between;
  align-items: flex-end;
  direction: ltr !important;

  li {
    width: 100%;
    height: 38vh;
    border-radius: 15px;

    &:nth-child(1),
    &:nth-child(2) {
      width: 48%;

      @media #{$media-700} {
        display: none;
      }
    }

    div {
      height: 100%;
    }

    @media #{$media-700} {
      height: 25vh;
    }
  }
}

.media_bloxk_01 {
  width: 53.3%;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.media_bloxk_02 {
  width: 46%;
}

.media_item_content {
  border-radius: 0px;
  border-top-right-radius: 15px;
  border-top-left-radius: 15px;
  padding: 4%;
  background: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 1);

  .latest_lable {
    font-size: 15px;
    display: inline-block;
  }

  h5 {
    font-size: 1.375rem;
    line-height: 2rem;
    font-weight: 300;
    color: #000000;

    @media #{$media-700} {
      font-size: 2rem;
      line-height: 2.8rem !important;
    }
  }

  .date_lable {
    font-size: 14px;
    color: #868686;
  }
}

.media_item_image {
  height: 355px;
  width: 100%;
  border-bottom-right-radius: 15px;
  border-bottom-left-radius: 15px;
  overflow: hidden;

  @media #{$media-1600} {
    height: 300px;
  }

  img {
    transition: all 0.5s ease-in-out;
  }

  // transition: all 0.4s ease-out 0s;
  // -moz-transition: all 0.4s ease-out 0s;
  // -webkit-transition: all 0.4s ease-out 0s;
  // -o-transition: all 0.4s ease-out 0s;

  &:hover {
    img:not(.holder) {
      transform: scale(1.1);
    }
  }
}

.btn_read {
  display: flex;
  align-items: center;
  color: #574547;
  font-size: 14px;
  line-height: 24px;
  gap: 10px;

  &:hover {
    font-size: 14.3px;
    transition: all 0.1s ease-in-out;

    img {
      transform: translateX(5px);
    }
  }

  img {
    transition: all 0.4s ease-out 0s;
    -moz-transition: all 0.4s ease-out 0s;
    -webkit-transition: all 0.4s ease-out 0s;
    -o-transition: all 0.4s ease-out 0s;
  }
}

.media_item_02 {
  margin: -2% 0%;
  display: flex;
  flex-wrap: wrap;

  li {
    list-style: none;
    width: 50%;
    padding: 2%;

    .media_item_content {
      p {
        font-size: 1.25rem;
        color: #241F21;
        margin-bottom: 5px;
      }
    }

    .media_item_content {
      height: 100%;
      border-radius: 15px;
      padding: 9%;
    }
  }
}

.testimonial_slide {
  border: 1px solid rgba(128, 94, 89, 0.1);
  background: #f5ede6;
  border-radius: 30px;
  padding: 14% 10% 13%;
  width: 100%;

  p {
    font-size: 1.125rem;
    line-height: 1.625rem;
    margin-bottom: 30px;
    color: #000;

    @media #{$media-700} {
      font-size: 1.8rem;
      line-height: 2.3rem;
    }
  }

  .mb_30 {
    img {
      @media #{$media-700} {
        width: 40px;
      }
    }
  }
}

.testimonial_profile_wrap {
  display: flex;
  align-items: center;
  gap: 20px;
  align-items: center;
}

.testimonial_image {
  width: 78px;
  min-width: 78px;
  height: 78px;
  border-radius: 50%;
  overflow: hidden;

  @media #{$media-1600} {
    width: 65px;
    height: 65px;
    min-width: 65px;
  }

  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}

.testimonial_profile {
  width: calc(100% - 78px);

  span {
    font-weight: 700;
    font-size: 1.375rem;

    @media #{$media-700} {
      font-size: 2rem;
    }
  }

  p {
    margin: 0;
    color: #777777;
    font-size: 1rem;

    @media #{$media-700} {
      font-size: 1.7rem;
    }
  }
}

.service_item_mob {
  height: unset !important;
  width: 50% !important;
  border-radius: 20px;
  overflow: hidden;

  @media #{$media-700} {
    border-radius: 15px;
  }

  .service_image {
    height: 100%;
  }

  .service_image {
    height: 55vw;

    @media #{$media-820} {
      height: 80vw;
      width: 100%;
      position: relative;
    }
  }

  .text {
    z-index: 800;
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;

    &:after {
      @media #{$media-820} {
        content: '';
        position: absolute;
        width: 100%;
        height: 140px;
        bottom: -5px;
        left: 0;
        background: linear-gradient(180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.5) 100%);
        z-index: 801;
      }
    }

    h3 {
      color: #fff;
      font-size: 2rem;

      @media #{$media-820} {
        position: relative;
        z-index: 802;
      }
    }
  }

  @media #{$media-820} {
    width: 80% !important;
  }
}

.mob_padding_slider {
  padding-bottom: 50px;
  padding-right: 0;
}

.custom_pagination {
  display: inline-flex;
  margin-top: 35px;
  width: 100%;
  justify-content: center;
}

.btn_link_1 {
  backdrop-filter: blur(19.299999237060547px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.2);
}

.mySwiper_media {
  .swiper-slide {
    height: unset !important;
  }

  .media_item_content {
    height: 100%;

    @media #{$media-768} {
      height: fit-content;
    }
  }
}

.mt_auto {
  margin-top: auto !important;
}

.media_mob {
  .media_item_content {
    display: flex;
    flex-wrap: wrap;

    @media #{$media-768} {
      gap: 30px;
      flex-grow: 1;
    }
  }

  .media_item_content {
    border: none;
  }
}

.media_read_more_btn {
  display: none;

  @media #{$media-768} {
    display: inline-flex;
  }
}

// media_item_content

.overflow_hide {
  overflow: hidden;
}

.corporate_section_padding_mobile {
  overflow: hidden;

  @media #{$media-820} {
    padding-top: 100px;
    padding-bottom: 100px;
  }

  @media #{$media-700} {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}

.image_block_style {
  border-radius: 15px;
  display: block;
  overflow: hidden;
  box-shadow: 20px 13px 31px 0px rgba(87, 70, 72, 0.2);

  img {
    transition: all 0.3s ease;
  }

  &:hover {
    img:not(.holder) {
      transform: scale(1.1);
    }
  }
}

// ----------amal------------

.swiper_card_section {
  padding: 80px 0 130px;
  width: 100%;
  background-color: #f5ede6;

  .swiper_card_container {
    h4 {
      color: #574547;
      // font-size: 40px;
      font-size: 2.5rem;
      line-height: 112%;
      font-weight: 700;
      padding-bottom: 35px;

      @media #{$media-1440} {
        padding-bottom: 20px;
      }

      @media #{$media-950} {
        font-size: 3rem;
        padding-bottom: 0px;
        margin-bottom: 20px;
      }
    }
  }

  @media #{$media-1440} {
    padding: 70px 0 110px;
  }

  @media #{$media-950} {
    padding: 50px 0;
  }

  @media #{$media-480} {
    padding: 30px 0;
  }

  &:last-child {
    padding: 80px 0 50px;

    @media #{$media-1440} {
      padding: 70px 0 40px;
    }

    @media #{$media-950} {
      padding: 50px 0;
    }

    @media #{$media-480} {
      padding: 30px 0;
    }
  }

  .card_box {
    cursor: default !important;

    &:hover {

      .card_img {
        .image_block_style {
          img {
            transform: unset !important;
          }
        }

      }
    }
  }
}

.card_flip_container {
  h3 {
    color: #241f21;
    // font-size: 45px;
    font-size: 2.8125rem;
    font-weight: 700;
  }
}

.card_group_sec {
  display: flex;
  margin-left: -1.5%;
  margin-right: -1.5%;
  flex-wrap: wrap;
  row-gap: 25px;

  .card_box {
    list-style: none;
    margin: 0 1.5%;
    width: 22%;
    border-radius: 15px;
    box-shadow: 20px 13px 31px 0px rgba(87, 70, 72, 0.2);
    overflow: hidden;
    cursor: pointer;
    position: relative;
    z-index: 5;

    .card_img {
      height: 100%;
      width: 100%;

      position: relative;

      img {
        display: block;
        height: auto;
        width: 100%;
        object-fit: contain;
        transition: all 0.7s ease;
      }

      .card_content {
        position: absolute;
        z-index: 5;
        left: 50%;
        width: 100%;
        bottom: 35px;
        transform: translateX(-50%);
        text-align: center;

        h5 {
          color: #ffffff;
          // font-size: 26px;
          font-size: 1.625rem;
          line-height: 28px;
          font-weight: 600;
          margin-bottom: 15px;

          @media #{$media-1440} {
            font-size: 1.5rem;
            margin-bottom: 10px;
          }

          @media #{$media-1024} {
            margin-bottom: 5px;

            line-height: normal;
          }

          @media #{$media-700} {
            font-size: 16px;
          }

          @media #{$media-480} {
            font-size: 15px;
          }
        }

        .card_count {
          color: #ffffff;
          // font-size: 30px;
          font-size: 1.875rem;
          line-height: 28px;
          font-weight: 100;
          display: none;

          span {
            color: #ffffff;
            font-size: 18px;
            line-height: 28px;
            font-weight: 100;
            padding: 0 5px;

            @media #{$media-1440} {
              font-size: 17px;
            }

            @media #{$media-700} {
              font-size: 15px;
              line-height: normal;
            }
          }

          @media #{$media-1440} {
            font-size: 1.7rem;
          }

          @media #{$media-700} {
            font-size: 18px;
            line-height: normal;
          }

          @media #{$media-480} {
            font-size: 16px;
          }
        }

        @media #{$media-1024} {
          bottom: 20px;
        }
      }
    }

    &:hover .card_img img:not(.holder) {
      transform: scale(1.15);
    }

    @media #{$media-950} {
      width: 47%;
    }
  }

  &.premium_sec {
    li {
      width: calc(33.333% - 3%);

      @media #{$media-600} {
        width: 47%;
      }
    }
  }

  &.card_group_sec_two_cl {
    li {
      // width: 47%;

      .card_content {
        h5 {
          font-size: 1.813rem !important;
          font-weight: 400 !important;
        }
      }
    }

    .card_content {
      h5 {
        font-size: 2.5rem !important;

        @media #{$media-700} {
          font-size: 2rem !important;
        }
      }
    }
  }

  &.pt_100 {
    @media #{$media-480} {
      padding-top: 20px;
    }
  }
}
.learn_card_group_sec{
  margin: 0;
  row-gap: 100px;
  >li{
    overflow: hidden;
    border-radius: 15px;
  }
  @media #{$media-1800}{
    // >li{
    //   height: 45.6vw;
    // }
  }
  @media #{$media-1440}{
    row-gap: 90px;
  }
  @media #{$media-1200}{
    row-gap: 80px;
  }
  @media #{$media-1024}{
    row-gap: 70px;
    >li{
      height: 43.6vw;
    }
  }
  @media #{$media-820}{
    row-gap: 50px;
    >li{
    overflow: unset;
    border-radius: 0;
    height: unset;
    }
  }
  
}

.modal_container {
  width: 90%;
  max-width: 900px;
  background-color: #f5ede6 !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  border-radius: 15px;
  transform: translate(-50%, -50%) !important;
  padding: 30px 3% !important;

  .modal_inside {

    &::-webkit-scrollbar {
      background-color: transparent !important;
    }

    &::-webkit-scrollbar-track {
      margin-top: 30px !important;
      margin-bottom: 30px !important;
    }
  }

  .modal_close {
    position: absolute;
    top: 20px;
    right: unset;
    inset-inline-end: 20px;
    border-radius: 50%;
    background-color: #805e59;

    &:hover {
      background-color: #5a3c37;
    }

    svg {
      fill: #ffffff !important;
    }

    @media #{$media-768} {
      width: 35px;
      height: 35px;
    }
  }

  .modal_discount_sec {
    position: absolute;
    top: 40px;
    inset-inline-end: 100px;

    span {
      color: #241f21;
      // font-size: 26px;

      font-size: 1.625rem;
      line-height: 28px;
      font-weight: 700;

      @media #{$media-768} {
        font-size: 2.625rem;
      }
    }

    p {
      color: #000000;
      font-size: 13px;
      line-height: 28px;
      font-weight: 300;

      @media #{$media-480} {
        line-height: 120%;
      }
    }

    @media #{$media-768} {
      top: 40px;
      right: 30px;
    }

    @media #{$media-480} {
      width: 100%;
      position: unset;
    }
  }

  h4 {
    // font-size: 26px;
    font-size: 1.625rem;
    color: #241f21;
    line-height: 28px;
    font-weight: 600;
    margin-bottom: 10px;

    @media #{$media-768} {
      font-size: 16px;
      margin-bottom: 5px;
      line-height: normal;
    }
  }

  .modal_content_group {
    padding: 10px 0;

    h5 {
      color: #805e59;
      // font-size: 20px;

      font-size: 1.25rem;
      line-height: 28px;
      font-weight: 500;
      margin-bottom: 10px;

      @media #{$media-768} {
        font-size: 16px;
        margin-bottom: 5px;
      }
    }

    ul {
      li {
        padding: 0 20px;
        position: relative;
        color: var(--black, #000000);
        font-size: 15px;
        line-height: 199%;
        font-weight: 400;

        &::after {
          content: "";
          position: absolute;
          inset-inline-start: 0;
          top: 7px;
          background-image: url("/images/list-arrow.svg");
          background-position: center;
          background-repeat: no-repeat;
          background-size: contain;
          height: 12px;
          width: 12px;

          @media #{$media-768} {
            top: 10px;
            height: 9px;
            width: 9px;
          }
        }

        ol {
          border: 1px solid white;
          border-radius: 15px;
          background: rgba(255, 255, 255, 0.4);
          padding: 7px 30px;
          width: 85%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          margin: 20px 0;

          li {
            width: fit-content;
            position: unset;
            padding: 5px 0;

            @media #{$media-500} {
              padding: 0px 0px;
              line-height: 160%;
            }
          }

          @media #{$media-768} {
            width: 95%;
            padding: 7px 20px;
          }

          @media #{$media-480} {
            margin: 10px 0;
          }
        }

        @media #{$media-768} {
          font-size: 14px;
        }

        @media #{$media-500} {
          line-height: 165%;
        }
      }
    }

    @media #{$media-500} {
      padding: 5px 0;
    }
  }
}

// .card_image_height {
//   height: 27.4vw;
//   max-height: 526px;

//   @media #{$media-480} {
//     height: 57.4vw;
//   }
// }

.backgroundHover {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  z-index: 30;
  transition: all 1.5s ease;

  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    display: block;
  }

  &.active {
    opacity: 1 !important;
  }

  @media #{$media-768} {
    background-color: hwb(28 90% 4% / 0.8);

    img {
      display: none;
    }
  }
}

// -------Corporate Events---------

.corporate_events_section {
  // overflow: hidden;

  .corporate_container {
    text-align: center;
    position: relative;
    z-index: 2;

    @media #{$media-700} {
      text-align: start;
    }

    h2 {
      color: #241f21;
      // font-size: 45px;
      font-size: 2.8125rem;
      line-height: 120%;
      font-weight: 700;
      text-transform: capitalize;

      @media #{$media-700} {
        text-align: start;
      }
    }

    p {
      width: 100%;
      max-width: 1140px;
      color: #000000;
      font-size: 16px;
      line-height: 27px;
      font-weight: 400;
      margin: auto;

      @media #{$media-768} {
        font-size: 15px;
        line-height: 180%;
      }

      @media #{$media-700} {
        text-align: start;
      }
    }
  }
}

.swiper_container {
  width: 100%;

  .card_img {
    display: block;
    height: auto;
    width: 100%;
    border-radius: 15px;
    overflow: hidden;

    img {
      display: block;
      height: auto;
      width: 100%;
      object-fit: contain;
    }
  }
}

.event_enquiry_section {
  .event_enquiry_container {
    background-color: #f5ede6;
    border-radius: 55px;
    width: 100%;
    padding: 50px 4%;

    h3 {
      color: #241f21;
      // font-size: 45px;
      font-size: 2.8125rem;
      line-height: 120%;
      font-weight: 700;
      margin-bottom: 15px;
    }

    >p {
      color: #000000;
      font-size: 16px;
      line-height: 20px;
      font-weight: 400;

      @media #{$media-768} {
        font-size: 15px;
        line-height: 150%;
      }
    }

    .form_li {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: 20px;

      .textarea_wrapp {
        flex-grow: 1;

        textarea {
          height: auto;
        }
      }

      >li {
        display: flex;
        width: 40%;

        &:nth-child(1) {
          width: calc(60% - 20px);
        }

        .form_input {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          margin: 0 -1%;
          width: calc(100% + 2%);

          >li {
            width: 48%;
            margin: 10px 1%;

            @media #{$media-500} {
              width: 98%;
              margin: 7px 1%;
            }
          }
        }

      }


      @media #{$media-768} {
        flex-direction: column;
        gap: 10px;

        li {
          width: 100%;

          &:nth-child(1) {
            width: 100%;
          }
        }
      }

      @media #{$media-500} {
        gap: 7px;
      }
    }

    .submit_btn {
      padding-left: 45px;
      padding-right: 60px;
    }

    @media #{$media-1024} {
      border-radius: 30px;
    }

    @media #{$media-768} {
      padding: 40px 4%;
    }

    @media #{$media-500} {
      border-radius: 25px;
    }
  }
}

.form_input_field {
  width: 100%;
  border-radius: 35px;
  border: 1px solid rgb(255, 255, 255);
  height: 44px;
  padding: 0 20px;
  background-color: white;
  display: block;
  font-size: 15px;
  color: #574547;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;

  &::placeholder {
    color: #574547;
  }

  &.form_textarea_field {
    height: 100%;
    min-height: 110px;
    border-radius: 20px;
    resize: none;
    padding-top: 15px;
    padding-bottom: 15px;
  }

  @media #{$media-768} {
    font-size: 16px;
  }

  // @media #{$media-500} {
  //   border-radius: 15px !important;
  //   height: 37px;
  // }
}

.form_select_field {
  position: relative;

  select {
    color: #574547;
    background-image: url("/images/dropdown-arrow.svg");
    background-repeat: no-repeat;
    background-position: calc(100% - 20px) center;

    option {
      background-color: #dddddd;
      color: #272727;
      padding: 15px 15px;
    }
  }

  // &::after {
  //   position: absolute;
  //   content: " "; // Ensure this is meaningful
  //   top: 50%;
  //   transform: translateY(-50%);
  //   right: 15px;
  //   height: 8px;
  //   width: 15px;
  //   z-index: 20;
  //   background-image: url("/images/dropdown-arrow.svg");
  //   background-repeat: no-repeat;
  //   background-size: contain;
  //   background-position: center;
  //   transition: all 0.3s ease;
  // }
}

// -------Corporate Events ends---------

// -------Tournament---------

.upcoming_tournament_container {
  h3 {
    color: #241f21;
    // font-size: 45px;
    font-size: 2.8125rem;
    line-height: 120%;
    font-weight: 700;
  }

  .tournament_card_sec {
    width: 100%;

    >li {
      width: 100%;
      border-radius: 20px;
      box-shadow: 15px 5px 15px rgba(0, 0, 0, 0.103);
      overflow: hidden;
      position: relative;
      margin-bottom: 30px;

      .card_data_bg_img {
        width: 100%;
        display: none;

        img {
          display: block;
          object-fit: contain;
          height: auto;
          width: 100%;
        }
      }

      .card_data_container {
        width: 100%;
        padding: 60px 5%;
        min-height: 512px;

        @media #{$media-1440} {
          min-height: 470px;
        }

        @media #{$media-1440} {
          min-height: unset;
        }

        &.color1 {
          background: linear-gradient(-90deg,
              rgba(87, 69, 71, 0) 40%,
              rgba(19, 136, 49, 1) 100%);

          @media #{$media-1024} {
            background: linear-gradient(-90deg,
                rgba(87, 69, 71, 0) 10%,
                rgba(19, 136, 49, 1) 100%);
          }

          @media #{$media-600} {
            background: linear-gradient(-90deg,
                rgba(87, 69, 71, 0) -10%,
                rgba(19, 136, 49, 1) 100%);
          }
        }

        &.color2 {
          background: linear-gradient(-90deg,
              rgba(87, 69, 71, 0) 30%,
              #4d243d 80%,
              #4d243d 100%);

          @media #{$media-1024} {
            background: linear-gradient(-90deg,
                rgba(87, 69, 71, 0) 20%,
                #4d243d 80%,
                #4d243d 100%);
          }

          @media #{$media-600} {
            background: linear-gradient(-90deg,
                rgba(87, 69, 71, 0) -86%,
                #4d243d 68%,
                #4d243d 100%);
          }
        }

        .card_box {
          width: 100%;

          .card_logo {
            height: auto;
            width: 220px;
            // width: 20%;

            >img {
              height: auto;
              width: 100%;
              display: block;
              object-fit: contain;
            }

            @media #{$media-1440} {
              width: 180px;
            }

            @media #{$media-1280} {
              width: 20%;
            }

            @media #{$media-600} {
              width: 30%;
            }

            @media #{$media-500} {
              width: 35%;
            }
          }

          h4 {
            color: #ffffff;
            // font-size: 40px;
            font-size: 2.5rem;
            line-height: 44px;
            font-weight: 500;
            margin-top: 20px;
            max-width: 518px;

            @media #{$media-768} {
              line-height: 130%;
            }

            @media #{$media-600} {
              br {
                display: none;
              }
            }

            @media #{$media-500} {
              margin-top: 5px;
            }
          }

          .place_list {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            gap: 20px;
            row-gap: 10px;
            margin-top: 40px;

            li {
              width: auto;
              color: #ffffff;
              font-size: 16px;
              font-weight: 300;
              display: flex;
              align-items: center;
              gap: 15px;

              .list_icon {
                height: auto;
                width: 20px;

                >img {
                  height: auto;
                  width: 100%;
                  display: flex;
                  object-fit: contain;
                }

                &.location {
                  width: 15px;

                  @media #{$media-768} {
                    width: 12px;
                  }
                }

                @media #{$media-768} {
                  width: 15px;
                }
              }

              @media #{$media-768} {
                gap: 8px;
              }

              @media #{$media-600} {
                font-size: 15px;
              }
            }

            @media #{$media-768} {
              margin-top: 20px;
            }

            @media #{$media-600} {
              row-gap: 5px;
            }
          }

          .button_list {
            display: flex;
            gap: 20px;
            width: 100%;
            margin-top: 15px;
            flex-wrap: wrap;

            .card_button {
              background-color: transparent;
              border: 1px solid rgba(255, 255, 255, 0.541);

              &::after {
                background-color: #f5ede6;
              }

              &:hover {
                color: #5a3c37;
              }

              &:hover span {
                background-color: #744e48;
              }
            }

            .card_fill_btn {
              background-color: #ffffff;
              border: none;
              color: #574547;

              &:hover {
                color: #000000;
                // background-color: #744e48;
              }

              &::after {
                background-color: #744e48;
              }

              span {
                background-color: #805e59;
              }
            }

            @media #{$media-768} {
              margin-top: 5px;
              gap: 15px;
            }

            @media #{$media-500} {
              margin-top: 5px;
              gap: 5px;
            }
          }
        }

        @media #{$media-768} {
          padding: 30px 6%;
        }
      }

      @media #{$media-500} {
        margin-bottom: 20px;
        border-radius: 15px;
      }
    }
  }
}

// ------

.past_tournament_container {
  >h3 {
    color: #241f21;
    // font-size: 45px;
    font-size: 2.8125rem;
    font-weight: 700;

    @media #{$media-1024} {
      margin-bottom: 15px;
    }

    @media #{$media-820} {
      margin-bottom: 10px;
      line-height: normal;
    }
  }

  >span {
    color: #241f21;
    // font-size: 30px;
    font-size: 1.875rem;
    line-height: 120%;
    font-weight: 700;
  }
}

.past_tournament_section {
  width: 100%;

  .past_tournament_card_list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -1%;
    padding-top: 50px;
    row-gap: 30px;

    >li {
      width: calc(33.333% - 2%);
      margin: 0 1%;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 20px 13px 31px 0px rgba(87, 70, 72, 0.2);
      // box-shadow: 7px 7px 15px rgba(0, 0, 0, 0.274);
      cursor: pointer;

      .past_tournament_bg_img {
        height: auto;
        width: 100%;
        position: relative;

        >img {
          display: block;
          object-fit: contain;
          height: auto;
          width: 100%;
        }

        &::after {
          position: absolute;
          content: "";
          height: 50%;
          width: 100%;
          bottom: 0;
          left: 0;
          background: linear-gradient(180deg,
              rgba(0, 0, 0, 0) 0%,
              #292021e8 100%);
          transition: all 0.3s ease;
        }

        &:hover::after {
          opacity: 0;
        }

        &::before {
          position: absolute;
          content: "";
          height: 100%;
          width: 100%;
          bottom: 0;
          left: 0;
          background-color: #080808;
          opacity: 0;
          transition: all 0.3s ease;
        }

        &:hover::before {
          opacity: 0.4;
        }

        .past_card_content {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          padding: 30px 7%;
          z-index: 3;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .past_card_logo {
            height: auto;
            max-width: 150px;

            >img {
              height: auto;
              width: 100%;
              object-fit: contain;
              display: none;

              &:nth-child(2) {
                display: block;
              }
            }

            @media #{$media-1024} {
              max-width: 115px;
            }

            @media #{$media-820} {
              max-width: 100px;
            }

            @media #{$media-500} {
              max-width: 80px;
            }
          }

          >p {
            color: #ffffff;
            text-align: center;
            // font-size: 26px;
            font-size: 1.625rem;
            line-height: 1.75rem;
            font-weight: 400;
            padding-bottom: 15px;
            width: 100%;
            transition: all 0.5s ease;

            @media #{$media-1280} {
              padding-bottom: 0;
            }

            @media #{$media-1024} {
              margin-bottom: 0;
            }

            @media #{$media-820} {
              line-height: normal;
            }
          }

          .sub_card_content {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            backdrop-filter: blur(5px);

            background: rgba(255, 255, 255, 0.4);
            bottom: -100%;
            width: calc(100% - 30px);
            border-radius: 15px;
            border: 1px solid white;
            padding: 30px 10%;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.7s ease;
            max-height: calc(100% - 25px);
            overflow-y: auto;

            >h5 {
              color: #805e59;
              text-align: center;
              // font-size: 30px;
              font-size: 1.875rem;
              font-weight: 700;

              @media #{$media-1280} {
                margin-bottom: 15px;
              }

              @media #{$media-1024} {
                margin-bottom: 10px;
              }

              @media #{$media-500} {
                margin-bottom: 5px;
              }
            }

            >p {
              color: #000000;
              text-align: center;
              // font-size: 20px;
              font-size: 1.25rem;
              font-weight: 400;

              @media #{$media-1280} {
                margin-bottom: 10px;
                line-height: normal;
              }

              @media #{$media-500} {
                font-size: 13px;
                margin-bottom: 5px;
              }
            }

            >span {
              color: #000000;
              text-align: center;
              // font-size: 30px;
              font-size: 1.875rem;
              font-weight: 700;
            }

            @media #{$media-1280} {
              padding: 25px 10%;
            }

            @media #{$media-1024} {
              padding: 20px 5%;
              width: calc(100% - 20px);
              border-radius: 8px;
            }

            @media #{$media-820} {
              padding: 15px 5%;
              width: calc(100% - 20px);
              border-radius: 8px;
            }
          }

          &:hover>p {
            opacity: 0;
          }

          &:hover .past_card_logo>img:nth-child(1) {
            display: block;
          }

          &:hover .past_card_logo>img:nth-child(2) {
            display: none;
          }

          @media #{$media-820} {
            padding: 25px 6%;
          }

          @media #{$media-500} {
            padding: 15px 6%;
          }
        }
      }

      @media #{$media-500} {
        width: 48%;
      }
    }

    @media #{$media-1024} {
      padding-top: 30px;
    }

    @media #{$media-820} {
      row-gap: 20px;
    }

    @media #{$media-600} {
      padding-top: 20px;
    }
  }
}

.past_tournament_section {
  width: 100%;

  .past_tournament_card_list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -2%;
    padding-top: 50px;
    row-gap: 40px;

    @media #{$media-1600} {
      row-gap: 35px;
    }

    @media #{$media-500} {
      margin: 0 -1%;
    }

    >li {
      width: calc(33.333% - 4%);
      margin: 0 2%;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 20px 13px 31px 0px rgba(87, 70, 72, 0.2);
      // box-shadow: 7px 7px 15px rgba(0, 0, 0, 0.274);
      cursor: pointer;

      @media #{$media-500} {
        margin: 0 1%;
      }

      .past_tournament_bg_img {
        height: auto;
        width: 100%;
        position: relative;

        >img {
          display: block;
          object-fit: contain;
          height: auto;
          width: 100%;
        }

        &::after {
          position: absolute;
          content: "";
          height: 50%;
          width: 100%;
          bottom: 0;
          left: 0;
          background: linear-gradient(180deg,
              rgba(0, 0, 0, 0) 0%,
              #292021e8 100%);
          transition: all 0.3s ease;
        }

        &:hover::after {
          opacity: 0;
        }

        &::before {
          position: absolute;
          content: "";
          height: 100%;
          width: 100%;
          bottom: 0;
          left: 0;
          background-color: #000000;
          opacity: 0;
          z-index: 1;
          transition: all 0.3s ease;
        }

        &:hover::before {
          opacity: 0.4;
        }

        .past_card_content {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          padding: 30px 7%;
          z-index: 3;
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          justify-content: space-between;

          .past_card_logo {
            height: auto;
            max-width: 150px;
            max-height: 150px;
            display: inline-flex;

            >img {
              height: auto;
              width: 100%;
              object-fit: contain;
              display: none;

              &:nth-child(2) {
                display: block;
              }
            }

            @media #{$media-1024} {
              max-width: 115px;
              max-height: 80px;
            }

            @media #{$media-820} {
              max-width: 100px;
              max-height: 70px;
            }

            @media #{$media-500} {
              max-width: 80px;
              max-height: 60px;
            }
          }

          >p {
            color: #ffffff;
            text-align: center;
            // font-size: 26px;
            font-size: 1.625rem;
            line-height: 1.75rem;
            font-weight: 400;
            padding-bottom: 15px;
            transition: all 0.5s ease;

            @media #{$media-1280} {
              padding-bottom: 0;
            }

            @media #{$media-1024} {
              margin-bottom: 0;
            }

            @media #{$media-820} {
              line-height: normal;
            }
          }

          .sub_card_content {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            backdrop-filter: blur(5px);

            background: rgba(255, 255, 255, 0.4);
            bottom: -100%;
            width: calc(100% - 30px);
            border-radius: 15px;
            border: 1px solid white;
            padding: 30px 10%;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.7s ease;
            max-height: calc(100% - 25px);
            overflow-y: auto;

            >h5 {
              color: #F6EEE6;
              text-align: center;
              // font-size: 30px;
              font-size: 1.875rem;
              font-weight: 400;

              @media #{$media-1280} {
                margin-bottom: 15px;
              }

              @media #{$media-1024} {
                margin-bottom: 10px;
              }

              @media #{$media-500} {
                margin-bottom: 5px;
              }
            }

            >p {
              color: #000000;
              text-align: center;
              // font-size: 20px;
              font-size: 1.25rem;
              font-weight: 400;

              @media #{$media-1280} {
                margin-bottom: 10px;
                line-height: normal;
              }

              @media #{$media-500} {
                font-size: 13px;
                margin-bottom: 5px;
              }
            }

            >span {
              color: #574547;
              text-align: center;
              // font-size: 30px;
              font-size: 1.875rem;
              font-weight: 700;
            }

            @media #{$media-1280} {
              padding: 25px 10%;
            }

            @media #{$media-1024} {
              padding: 20px 5%;
              width: calc(100% - 20px);
              border-radius: 8px;
            }

            @media #{$media-820} {
              padding: 15px 5%;
              width: calc(100% - 20px);
              border-radius: 8px;
            }
          }

          &:hover>p {
            opacity: 0;
          }

          &:hover .past_card_logo>img:nth-child(1) {
            display: block;
          }

          &:hover .past_card_logo>img:nth-child(2) {
            display: none;
          }

          @media #{$media-820} {
            padding: 25px 6%;
          }

          @media #{$media-500} {
            padding: 15px 6%;
          }
        }
      }

      @media #{$media-500} {
        width: 48%;
      }
    }

    @media #{$media-1024} {
      padding-top: 30px;
    }

    @media #{$media-820} {
      row-gap: 20px;
    }

    @media #{$media-600} {
      padding-top: 20px;
    }
  }
}

.past_tournament_bg_img:hover .sub_card_content {
  bottom: 15px !important;

  @media #{$media-1024} {
    bottom: 10px !important;
  }
}

// ---------------

.temp_style_01 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  li {
    width: 48.5%;
    padding-bottom: 20px;
  }

  &> :last-child {
    width: 100%;
  }

  &> :first-child,
  &> :nth-child(2) {
    @media #{$media-700} {
      display: none;
    }
  }
}

.spacing_class {
  padding-left: 5%;
  padding-right: 5%;
  width: 40%;
}

// -------Tournament Ends ---------

// --------flip Start---------
.flip_card {
  background-color: transparent;
  perspective: 3000px;
  width: calc(50% - 5%);
  margin: 0 1.5%;
  // max-height: 760px;
  max-height: 655px;
  height: 40vw;
  -webkit-perspective: 3000px;

  &:focus {
    outline: 0;
  }

  .flip_card_inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -webkit-transform-style: preserve-3d;
    @media #{$media-995} {
      height: auto;
    }
    @media #{$media-820} {
      box-shadow: 20px 13px 31px 0px rgba(87, 70, 72, 0.2);
    }

    .flip_card_front {
      position: absolute;
      z-index: 2;
      width: 100%;
      border-radius: 15px;
      overflow: hidden;
      -webkit-backface-visibility: hidden;
      height: 100%;
      background-color: white;

      &::after {
        content: "";
        position: absolute;
        height: 50%;
        width: 100%;
        z-index: 4;
        left: 0;
        bottom: 0;
        background-image: linear-gradient(to top,
            rgba(0, 0, 0, 0.568),
            rgba(0, 0, 0, 0));

        @media #{$media-820} {
          display: none;
        }
      }

      >img {
        width: 100%;
        object-fit: contain;
        display: block;
      }
      .image_block_style{
        img{
          object-position: top center;
        }
      }

      .card_content {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);

        z-index: 5;
        width: 100%;

        h5 {
          color: #ffffff;
          // font-size: 40px;
          font-size: 2.5rem;
          line-height: 112%;
          font-weight: 400;
        }


        @media #{$media-820} {
          display: none;
        }
      }

      @media #{$media-820} {
        position: unset;
        -webkit-backface-visibility: unset;
        border-radius: 0px;
      }
    }

    .flip_card_back {
      z-index: 1;
      transform: rotateY(180deg);
      -webkit-transform: rotateY(180deg);
      -webkit-backface-visibility: hidden;
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 15px;
      overflow: hidden;
      text-align: start;

      >img {
        height: auto;
        width: 100%;
        object-fit: contain;
        display: block;
      }

      .card_content_back {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5ede669;
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding: 40px 10% 20px 10%;
        z-index: 50;
        pointer-events: auto;

        @media #{$media-820} {
          background: #F5EDE6
        }

        h5 {
          color: #241f21;
          text-align: center;
          // font-size: 40px;
          font-size: 2.5rem;
          font-weight: 700;

          @media #{$media-1024} {
            margin-bottom: 10px;
          }
        }

        ul {
          overflow: auto;
          pointer-events: auto;
          width: 100%;

          li {
            width: 100%;
            border-radius: 10px;
            border: 1px solid white;
            background: rgba(255, 255, 255, 0.4);
            position: relative;
            padding: 10px 35px;
            margin-bottom: 15px;
            font-size: 16px;

            font-weight: 400;
            line-height: 150%;


            @media #{$media-820} {
              font-size: 14px;
            }

            &::after {
              content: "";
              position: absolute;
              left: 10px;
              top: 13px;
              background-image: url("/images/list-arrow.svg");
              background-position: center;
              background-repeat: no-repeat;
              background-size: contain;
              height: 17px;
              width: 17px;

              @media #{$media-820} {
                left: 8px;

                height: 15px;
                width: 12px;
              }
            }

            p {
              font-size: 16px;
              line-height: normal;
              font-weight: 400;
              line-height: 150%;
              margin-bottom: 0;

              @media #{$media-820} {
                font-size: 14px;
              }
            }

            @media #{$media-820} {
              padding: 10px 25px;
            }
          }
        }

        @media #{$media-820} {
          position: unset;
          padding: 20px 5% 20px 5%;

          >img {
            display: none;
          }
        }

        @media #{$media-500} {
          justify-content: unset;
          align-items: flex-start;
        }
      }

      @media #{$media-820} {
        transform: unset;
        -webkit-transform: unset;
        -webkit-backface-visibility: unset;
        position: unset;
        border-radius: 0;
      }
    }

    @media #{$media-820} {
      transform-style: unset;
      backface-visibility: unset;
      -moz-backface-visibility: unset;
      -webkit-transform-style: unset;
      border-radius: 10px;
      overflow: hidden;
    }
  }

  &:hover .flip_card_inner,
  &:focus .flip_card_inner {
    transform: rotateY(180deg);

    @media #{$media-820} {
      transform: unset;
    }
  }

  // @media #{$media-1440} {
  //   height: 45.6vw;
  // }

  @media #{$media-950} {
    height: 43.6vw;
  }

  @media #{$media-820} {
    height: unset;
    max-height: unset;
    -webkit-perspective: unset;
  }

  @media #{$media-500} {
    width: calc(100% - 3%);
  }
}

.mob_d_none {
  @media #{$media-820} {
    display: none;
  }
}

.mob_border0 {
  @media #{$media-820} {
    border-radius: 0;
  }
}

// --------flip end---------

.media_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;

  h3 {
    margin-bottom: 0;
  }

  a {
    color: #574547;
    font-size: 15px;
    font-weight: 400;
    display: flex;
    align-items: center;
    gap: 10px;

    &:hover {
      color: #8f8f8f;
    }

    @media #{$media-768} {
      display: none;
    }
  }

  @media #{$media-700} {
    margin-bottom: 25px;
  }
}

.golfers_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 50px;

  @media #{$media-1600} {
    margin-bottom: 40px;
  }

  h3 {
    margin-bottom: 0;
  }

  @media #{$media-700} {
    margin-bottom: 25px;
  }
}

.golfers_swiper_btn {
  width: auto;
  display: flex;
  position: relative;
  gap: 30px;
  inset-inline-end: 10px;

  @media #{$media-700} {
    inset-inline-end: 0;
  }

  >span {
    position: unset;

    @media #{$media-700} {
      display: flex !important;

      transform: rotate(180deg) scale(0.7) !important;

      &:nth-child(2) {
        transform: rotate(0deg) scale(0.7) !important;
      }
    }
  }

  @media #{$media-700} {
    gap: 5px;
  }
}

.media_swiper_btn {
  display: none;

  @media #{$media-768} {
    display: flex;
  }
}

// ----------amal end------------

.corporate_slider_box_2 {
  width: 37.2%;

  @media #{$media-820} {
    width: 100%;
  }
}

.corporate_content_box_2 {
  width: 50%;

  @media #{$media-700} {
    width: 100%;
  }
}

.corporate_slider_box_2a {
  width: 34.2%;

  @media #{$media-700} {
    width: 100%;
  }
}

.mask_after {
  position: relative;

  &::after {
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,000000+100&0+27,0.65+99 */
    background: linear-gradient(to bottom,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0) 27%,
        rgba(0, 0, 0, 0.65) 99%,
        rgba(0, 0, 0, 0.65) 100%);
    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */

    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
  }
}

// .h_experience_block{ height: 23.2vw; max-height: 445px;

//   @media #{$media-820} {
//     height: 50vw;
//   }

// }
// .corporate_slider_img_h{ height:35vw; max-height: 664px;

// @media #{$media-820} {
//   height: 100vw;
// }

// }

// .corporate_slider_img_h2{ height:22.8vw; max-height: 437px;

//   @media #{$media-820} {
//     height: 100vw;
//   }

//   }

// .h_dining_block{ height: 23vw; max-height: 441px;

//   @media #{$media-820} {

//     height: 40vw;
//   }

// }
// .h_dining_block_2{ height:17.5vw; max-height: 335px;

//   @media #{$media-820} {
//     height:32.5vw;
//   }

// }

.corporate_content_box_w_2 {
  width: 47%;

  @media #{$media-820} {
    width: 100%;
  }
}

.time_table_block {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 0 -0.5%;

  li {
    list-style: none;
    width: 33%;
    padding: 0.5%;

    .time_table_block_2 {
      justify-content: space-between;
      align-items: center;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      background: rgba(255, 255, 255, 0.4);
      border-radius: 15px;
      border: solid 1px #ffffff;
      padding: 9%;

      p {
        font-size: 1.25rem;
        color: #241f21;
        padding-left: 10px;
        padding-right: 10px;
        margin-bottom: 0;

        @media #{$media-1600} {
          font-size: 1.1rem;

        }

        @media #{$media-700} {
          font-size: 1.6rem;
          line-height: 2.3rem;
          padding-left: 0;
          padding-right: 0;
        }
      }
    }

    .time_table_block_3 {
      display: flex;
      flex-wrap: wrap;

      @media #{$media-700} {
        width: 100%;
        margin-bottom: 15px;
      }

      p {
        @media #{$media-700} {
          padding-left: 10px;
          padding-right: 10px;
        }
      }
    }

    .time_table_block_4 {
      width: 15%;
      display: flex;
      justify-content: center;

      img {
        max-width: 100%;
      }

      @media #{$media-700} {
        display: none;
      }
    }
  }



  &> :first-child {
    width: 100%;

    .time_table_block_2 {
      padding: 3%;
    }
  }
}

.corporate_content_box_w_3 {
  width: 42%;
  margin-right: 5%;

  @media #{$media-820} {
    width: 100%;
    margin-right: 0;
  }
}

// ------------insta-block--start
.insta_block_ul {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1%;
  justify-content: center;
  padding-left: 5%;
  padding-right: 5%;

  @media #{$media-700} {
    margin: 0 -7.5px
  }

  li {
    list-style: none;
    padding: 1%;
    width: 16.5%;

    .insta_block_img {
      border-radius: 10%;
      overflow: hidden;
    }

    @media #{$media-820} {
      width: 32.5%;
    }

    @media #{$media-700} {
      padding: 7.5px;
      width: calc(50% - 15px);
    }
  }

  // .insta_block_img{ width: 100%; height: 11.5vw; max-height: 214px;

  //   @media #{$media-820} {

  //     height:17vw;
  //   }
  //   @media #{$media-700} {
  //   height: 35vw;
  //   }
  // }
  padding: 0.5%;
}

// ------------insta-block--end

.insta_img {
  &::after {
    content: url(../public/images/insta_img.svg);
    display: block;
    position: absolute;
    left: 15px;
    top: 15px;
  }
}

.insta_video {
  justify-content: center;
  align-items: center;
  display: flex;
  flex-wrap: wrap;

  &::after {
    content: url(../public/images/insta_video.svg);
    display: block;
    position: absolute;
  }
}

.tab_section {
  width: 100%;
  border-bottom: solid 1px #574547;
}

.container_tab {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  li {
    padding-left: 1.5%;
    padding-right: 1.5%;

    a {
      padding: 35px 25px;
      display: inline-flex;
      position: relative;

      @media #{$media-1600} {
        padding: 25px 20px;
      }

      @media #{$media-950} {
        padding: 20px 15px;
      }

      &.active_tab {
        &:after {
          display: block;
          width: 100%;
          left: 0;
          bottom: -4px;
          background: #574547;
          content: "";
          position: absolute;
          height: 6px;
        }
      }
    }
  }
}

.list_style_01 {
  margin: 0;
  padding: 0;

  li {
    list-style: none;
    // background: url(../public/images/list_style.svg) no-repeat 0px 7px;
    padding-left: 35px;
    padding-right: 35px;
    font-size: 16px;
    margin-bottom: 5px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 10px;
      top: 4px;
      background-image: url(/images/list-arrow.svg);
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
      height: 18px;
      width: 18px;
    }
  }
}

.contact_list {
  display: flex;
  flex-wrap: wrap;


  li {
    display: flex;
    align-items: center;
    padding-right: 4%;

    @media #{$media-700} {
      padding-bottom: 10px;
    }

    @media #{$media-480} {
      width: 100%;
    }
  }
}

.contact_list_icn {
  width: 45px;
  height: 45px;
  margin-left: 10px;
  margin-right: 10px;
  border-radius: 100%;
  background: #805e59;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.border_style {
  border: solid 1px #002c3d;
}

.two_col_wrap {
  ul {
    margin-left: -3%;
    margin-right: -3%;
    padding: 0;
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;
      width: 50%;
      padding: 0 3%;

      .block_03 {
        width: 100%;
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.4);
        border: solid 1px #ffffff;
        padding: 5%;
      }

      @media #{$media-700} {
        width: 100%;
      }
    }

    li+li {
      @media #{$media-700} {
        margin-top: 30px;
      }
    }
  }
}

.amound_block_01 {
  margin-left: 20px;
  margin-right: 20px;
  width: 100%;
  padding: 3% 4%;
  display: inline-flex;
  background: linear-gradient(90deg,
      rgba(128, 94, 89, 0.2) 0%,
      rgba(128, 94, 89, 0) 100%);
  position: relative;

  p {
    font-size: 1.5rem;
    color: #241f21;
    line-height: 1.2rem;
    margin-bottom: 0;

    span {
      font-size: 14px;
      display: block;
      margin-top: 5px;
    }

    @media #{$media-700} {
      font-size: 2rem;
      line-height: 2.2rem;
    }
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    height: 100%;
    width: 22px;
    background: url(../public/images/after_content.svg) no-repeat center right;
    left: -22px;
    background-size: cover;
  }
}

.image_block_02 {
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
}

.colom_3_block {
  margin: 0 -1.5%;
  display: flex;
  flex-wrap: wrap;

  li {
    list-style: none;
    width: 33%;
    padding: 0% 1.5%;

    @media #{$media-700} {
      width: 100%;
    }
  }
}

.title_30 {
  margin-bottom: 10px;

  h3 {
    font-size: 1.875rem;
    color: #574547;
    font-weight: 600;
    margin: 0;

    @media #{$media-700} {
      font-size: 3rem;
      line-height: 100%;
    }
  }
}

.title_45 {
  h3 {
    font-size: 2.813rem;

    @media #{$media-700} {
      font-size: 4rem;
      line-height: 100%;
    }
  }
}

.text_block_02 {
  width: 100%;
  margin-bottom: 45px;

  .min_h {
    min-height: 200px;

    @media #{$media-1366} {
      min-height: 250px;
    }

    @media #{$media-1366} {
      min-height: 300px;
    }

    @media #{$media-820} {
      min-height: 360px;
    }

    @media #{$media-700} {
      min-height: unset;
    }
  }
}

.program_date_block_ul {
  background: rgba(255, 255, 255, 0.4);
  border: solid 1px #ffffff;
  border-radius: 15px;
  margin: 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 4%;
  margin-top: 25px;

  li {
    width: auto;
    padding: 0%;

    @media #{$media-700} {
      width: 100%;
      text-align: center;
      justify-content: center;
      padding-bottom: 7px;
      padding-top: 7px;
      margin-bottom: 7px;
      margin-top: 7px;
      border-bottom: solid 1px #fff;
    }

    // br{
    //   @media #{$media-700} {
    //     display: none;
    //   }
    // }
  }

  .program_date_block {
    width: 100%;
    position: relative;
    padding-left: 30px;

    img {
      position: absolute;
      left: 0;
      top: 3px;

      @media #{$media-1200} {
        left: unset !important;
        right: unset !important;
        top: 0px;
      }

      @media #{$media-700} {
        left: unset !important;
        right: unset !important;
        top: 0px;
      }
    }

    p {
      font-size: 1.438rem;
      margin-bottom: 0;

      span {
        font-size: 15px;

        @media #{$media-1440} {
          font-size: 13px;
        }
      }

      @media #{$media-1600} {
        font-size: 1.1rem;
        line-height: 1.3rem;
      }

      @media #{$media-700} {
        font-size: 2.5rem;
        line-height: 3.3rem;
      }
    }

    @media #{$media-700} {
      padding-left: 0;
      padding-right: 0;
      padding-top: 35px;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
    }

    @media #{$media-1200} {
      padding-left: 0;
      padding-top: 35px;
      justify-content: center;
      text-align: center;
      display: flex;
      flex-wrap: wrap;
    }
  }

  &> :last-child {
    border-bottom: none;
  }

  @media #{$media-1200} {
    padding: 15px 8%;
  }
}

.two_block_ul {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  align-items: center;

  >li {
    list-style: none;
    width: 47%;

    .max_w_txt {
      p {
        max-width: 75%;

        p {
          max-width: 75%;

          @media #{$media-700} {
            max-width: 100%;
          }
        }

        h5 {
          margin: 0;
          font-size: 1.375rem;
          margin-bottom: 3px;
        }

        @media #{$media-700} {
          max-width: 100%;
        }
      }

      @media #{$media-820} {
        width: 100%;
        margin-bottom: 20px;
      }

      @media #{$media-700} {
        width: 95%;
        margin-bottom: 20px;
      }
    }

    @media #{$media-700} {
      width: 100%;
    }
  }

  .list_ul {
    li {
      margin-bottom: 25px;
    }
  }

  @media #{$media-820} {
    align-items: flex-start;
  }

  @media #{$media-700} {
    gap: 15px;
  }
}

.two_cl_main {
  &> :nth-child(even) {
    .two_block_ul {
      flex-direction: row-reverse;
    }
  }
}

.corporate_block {
  background: #f5ede6;
  border-radius: 55px;
  display: flex;
  flex-wrap: wrap;
  padding: 4% 4%;

  .text_block_left {
    width: 80%;

    p {
      max-width: 80%;
      width: 100%;
    }

    @media #{$media-600} {
      width: 100%;
    }
  }

  .text_block_right {
    width: 20%;
    margin-top: 30px;

    @media #{$media-600} {
      width: 100%;
    }

    .contact_btn {
      min-width: 228px;

      @media #{$media-1440} {
        min-width: 210px;
      }

      @media #{$media-768} {
        min-width: auto;
      }
    }

    img {
      @media #{$media-700} {
        display: none;
      }
    }
  }

  @media #{$media-1024} {
    border-radius: 30px;
  }

  @media #{$media-700} {
    border-radius: 25px;
    padding: 8%;
  }
}

.corporate_select {

  width: 100%;
  border: 0;
  position: relative;
  display: block;
  margin-top: 5px;
  background-image: url("/images/icn_03.svg");
  background-repeat: no-repeat;
  background-position: right center;



  select {
    cursor: pointer;
    width: 100%;
    border: 0;
    font-size: 1.5rem;
    font-weight: 700;

    display: inline-block;
    margin-top: 5px;
    -moz-appearance: none;
    /* Firefox */
    -webkit-appearance: none;
    /* Safari and Chrome */
    appearance: none;
    padding: 0 15px;
    margin-inline-start: -15px;


    option {
      background-color: #dddddd;
      color: #272727;
      padding: 15px 15px;
      font-size: 1rem;
    }


  }

  .corporate_select_opt {
    padding: 10px;
    font-size: 1rem;

    /* Padding for each option */

  }

  // &::after {
  //   position: absolute;
  //   content: " "; // Ensure this is meaningful
  //   top: 50%;
  //   transform: translateY(-50%);
  //   right: 0px;
  //   height: 15px;
  //   width: 18px;
  //   z-index: 20;
  //   background-image: url("/images/icn_03.svg");
  //   background-repeat: no-repeat;
  //   background-size: contain;
  //   background-position: center;
  // }
}

.corporate_ul {
  margin: 0 -1%;
  padding: 0;
  display: flex;
  flex-wrap: wrap;

  li {
    list-style: none;
    width: 33%;
    padding: 1%;

    .corporate_ul_block {
      background: rgba(255, 255, 255, 0.4);
      border: solid 1px #ffffff;
      border-radius: 15px;
      padding: 5% 7%;
      position: relative;

      .icon_block {
        position: absolute;
        right: 6%;
        bottom: 9%;
      }

      p {
        margin: 0;

        // span {
        //   font-size: 1.5rem;
        //   font-weight: 700;
        //   display: inline-block;
        //   margin-top: 5px;
        // }

      }





    }

    .starting_txt {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      .starting_txt_icn {
        display: block;
        width: 40px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        @media #{$media-700} {
          width: 25px;
        }
      }

      .starting_txt_block {
        width: calc(100% - 40px);

        @media #{$media-700} {
          width: calc(100% - 30px);
        }
      }
    }

    .starting_txt_ul {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      align-items: center;
      justify-content: space-between;
    }

    .starting_txt_block {
      p {
        margin: 0;
      }

      h5 {
        margin: 0;
        font-size: 4rem;
        width: 100%;
        line-height: 4rem;
        font-weight: 600;

        .available {
          margin: 0;
          font-size: 2rem;
          width: 100%;
          line-height: 6rem;
          font-weight: 600;
        }

        span {
          font-size: 1.5rem;
          font-weight: 400;
        }
      }
    }

    @media #{$media-820} {
      width: 100%;
    }
  }
}

.feacherd_news {
  background: #f6ede6;
  border-radius: 20px;
  overflow: hidden;

  .media_item_image {
    height: auto;
    border-radius: 0;
  }
}

.news_list {
  margin-left: -0.5%;
  margin-right: -0.8%;
  display: flex;
  flex-wrap: wrap;

  li {
    list-style: none;
    width: 33%;
    padding-left: 0.8%;
    padding-right: 0.8%;
    margin-bottom: 35px;

    .news_list_main_block {
      border-radius: 15px;
      overflow: hidden;
      background: #f5ede6;
      height: 100%;

      .media_item_image {
        border-radius: 0;
      }
    }

    .media_item_content {
      padding: 6%;
      height: 100%;

      h5 {
        color: #574648;
        font-weight: 500;
        margin-top: 10px;
        line-height: 1.7rem;
      }
    }

    .media_item_image {
      height: auto !important;
    }

    @media #{$media-820} {
      width: 50%;
    }

    @media #{$media-400} {
      width: 100%;
    }
  }
}

.container_news {
  max-width: 1279px;
  padding: 2% 4%;
  border: 1px solid #f1dfd3;
  border-radius: 15px;
  margin-top: -100px;
  background: #fff;

  @media #{$media-820} {
    padding: 40px 5%;
  }

  h5 {
    font-size: 1.875rem;
    font-weight: 500;
  }

  .news_title {
    h1 {
      font-size: 2.5rem;
      color: #241f21;
      // max-width: 80%;
      line-height: 3rem;
      font-weight: 500;
    }
  }

  .image_block_news_detail {
    img {
      display: block;
    }
  }

  .news_txt_under_img {
    border-left: solid 5px #bf9173;
    padding: 0 2%;

    p {
      max-width: 50%;
      font-size: 14px;
      line-height: 20px;

      @media #{$media-700} {
        max-width: 100%;
      }
    }
  }
}

.news_list_related {
  background: #F5EDE6;

  .media_item_image {
    height: auto;
    border-radius: 0;
  }

  .media_item_content {
    // background: transparent;
    border: none;
  }

  border-radius: 15px;
  overflow: hidden;
  height: 100%;
}

.slider_nav {
  position: absolute;
  top: 50%;
  z-index: 200;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &::after {
    display: block;
    content: "";
    width: 46px;
    height: 46px;
    background: #805e59;
    -moz-transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
    -o-transform: rotate(135deg);
    -ms-transform: rotate(135deg);

    transform: rotate(135deg);
    transition: all 0.4s ease-out 0s;
    -moz-transition: all 0.4s ease-out 0s;
    -webkit-transition: all 0.4s ease-out 0s;
    -o-transition: all 0.4s ease-out 0s;

    @media #{$media-1600} {
      width: 40px;
      height: 40px;
    }
  }

  z-index: 150;

  &::before {
    display: block;
    content: "";
    position: absolute;

    z-index: 200;
    width: 17px;
    height: 25px;
    background: url(../public/images/slider_arrow.svg) no-repeat center center;
    transition: all 0.4s ease-out 0s;
    -moz-transition: all 0.4s ease-out 0s;
    -webkit-transition: all 0.4s ease-out 0s;
    -o-transition: all 0.4s ease-out 0s;

    @media #{$media-1600} {
      width: 17px;
      height: 20px;
    }
  }

  &.slider_prev {
    transform: rotate(180deg);
    left: -5%;

    @media #{$media-1600} {
      left: -19px;
    }
  }

  &.slider_next {
    right: -5%;

    @media #{$media-1600} {
      right: -19px;
    }
  }

  &:hover {
    &::after {
      background: #684a45;
    }

    &::before {
      transform: translateX(5px);
    }
  }

  @media #{$media-700} {
    display: none;
  }
}

.testimonial_block {
  position: relative;
  z-index: 200;
}

// ----------------new gallery css-----------

.container_gallery {
  max-width: 1590px;
}

.temp_style_02 {
  display: grid;
  grid-template-columns: 50% 50%;
  grid-template-rows: 75% 27% 100%;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
}

.grid_container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto;
  width: 100%;
  gap: 3%;

  li {
    list-style: none;
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // font-size: 1.5rem;
    // font-weight: bold;
  }

  .item_1 {
    grid-column: 2 / 3;
    grid-row: 1 / 3;
  }

  .item_2 {
    grid-column: 1 / 2;
    grid-row: 1 / 1;
  }

  .item_3 {
    grid-column: 1 / 2;
    grid-row: 2 / 3;
  }

  .image_block_style {
    width: 100%;
  }
}

.grid_container_2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto;
  grid-gap: 3%;
  gap: 3%;
  width: 100%;

  @media #{$media-700} {
    margin-bottom: 3%;
  }

  li {
    box-sizing: border-box;
  }

  .item_1 {
    grid-column: 1 / 2;
    grid-row: 1/1;
  }

  .item_2 {
    grid-column: 2 / 3;
    grid-row: 1 / 2;
  }

  .item_3 {
    grid-column: 1/3;
    grid-row: 2 / 3;
  }
}

.modal_container {

  .modal_inside {
    overflow: auto;
    max-height: 775px;

    @media #{$media-700} {
      max-height: 80vh;


    }
  }

  // &::after{
  //   content: '';
  //   position: absolute;
  //   width: 100%;
  //   height: 140px;
  //   background: linear-gradient(
  //   180deg,
  //   rgba(255, 255, 255, 0) 18.000000715255737%,
  //   rgba(251, 248, 245, 0.9) 75%,
  //   rgba(245, 237, 230, 1) 100%
  // );
  //   bottom:0;
  //   inset-inline-start: 0;
  //   border-bottom-left-radius:15px ;
  //   border-bottom-right-radius:15px ;
  // }



}

.gallery_block {
  // margin: 0 -1%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  >li {
    list-style: none;
    width: 49.3%;
    padding-bottom: 1.5%;

    @media #{$media-700} {
      width: 100%;
      padding-bottom: 3%;
    }
  }
}

.upcoming_tournament_container .tournament_card_sec>li .card_data_container {
  &.color1 {
    @media #{$media-820} {
      background: linear-gradient(-90deg,
          rgba(19, 136, 49, 1) 100%,
          rgba(19, 136, 49, 1) 100%);
    }
  }

  &.color2 {
    @media #{$media-820} {
      background: linear-gradient(-90deg,

          #4d243d 0%,
          #4d243d 100%) !important;
    }
  }
}

.tournament_card_sec {
  >li {
    .card_data_bg_img {
      @media #{$media-820} {
        display: block !important;
      }
    }
  }
}

.mySwiper_media {
  .mySwiper_media_block {
    width: 80%;
  }
}

.mediacenter_hm {
  @media #{$media-700} {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

.overflow_mob {
  @media #{$media-768} {
    overflow: hidden;
  }
}

.hover_expand {
  position: relative;
  z-index: 1;
}

.bg_hover_hm {
  @media #{$media-700} {
    padding-bottom: 0;
  }
}

.glofers_say_wrap {
  position: relative;
  z-index: 1;
}

.hide_section {
  display: none;
}

.load_more {
  justify-content: center;
  cursor: pointer;
}

.gallery_grid_new {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1%;

  li {
    width: 25%;
    padding: 1%;

    @media #{$media-700} {
      width: 50%;
    }
  }
}

.not_found_sec {
  background-color: #805e59;

  .not_found {
    text-align: center;
    min-height: 100dvh;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    ;

    h1 {
      font-size: 6rem;
      font-weight: 700;
      color: #ffffff;
    }

    h4 {
      font-size: 2rem;
      font-weight: 400;
      color: #ffffff;
    }

    .btn_link {
      background-color: transparent;
      border: 1px solid hsla(0, 0%, 100%, .541);
    }
  }
}

.news_date_ul {
  margin-left: -1%;
  margin-right: -1%;
  display: flex;

  li {
    padding: 0 1%;
    font-weight: 300;
    white-space: nowrap;
  }

}

.loder {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #f5ece5;
  z-index: 999;
  justify-content: center;
  display: flex;
  flex-wrap: wrap;
  align-items: center;


  .video_block {
    height: 650px;
    width: 650px;
    overflow: hidden;

    @media #{$media-950} {
      height: unset;
      width: unset;
    }

    .banner_video {
      width: 100%;
      margin-left: auto;
      margin-right: auto;
      height: auto;
      margin-bottom: -2px;
    }
  }
}

.privacy_policy {
  h2 {
    color: #241f21;
    font-size: 2.125rem;
    line-height: 110%;
    font-weight: 700;
    margin-bottom: 15px;
  }

  ul,
  ol {
    list-style: unset;
    margin-bottom: 20px;

    li {
      list-style: unset;
      list-style-position: inside;
    }
  }
}

.shadow_none {
  box-shadow: none;
}

.side_line {
  .welcome_txt_block {
    @media #{$media-700} {
      width: 95%;
      margin-left: unset;
      margin-right: unset;
    }
  }

  .corporate_content_box {
    @media #{$media-700} {
      width: 95% !important;
    }
  }

  .corporate_container {
    h2 {
      @media #{$media-700} {
        width: 95% !important;
      }
    }

    p {
      @media #{$media-700} {
        width: 95% !important;
        margin: unset;
      }
    }
  }
}

.dining {
  p {
    margin-bottom: 0;
  }
}

.desk_btn {
  @media #{$media-700} {
    display: none;
  }
}

.mob_section {
  margin: 0 auto;
  padding-bottom: 40px;
  justify-content: center;
  display: none;

  @media #{$media-700} {
    display: flex;
  }
}

.mob_btn {
  display: none;

  @media #{$media-700} {
    display: inline-flex;
  }
}

.mouse_default {
  cursor: default !important;

  img {
    transform: unset !important;
  }
}

// .ph_feild{
//   border-radius: 30px;
//   border: 1px solid #d3d3d3;
//   display: flex;
//   align-items: center;
//   >div{
//     padding-bottom: 0;
//   }
//   button{
//       border: none;
//   }
//   .form_input_field,.phnfield{
//       border: 0 !important;
//   }
//   li{
//     margin-bottom: 0 !important;
//     margin-top: 0 !important;
//   }
//   .phnfield{
//     padding-inline-start: 10px !important;
//   }
// }
// .ph_feild_event{
//   border: 0;
//   background-color: #ffffff;
// }
.ph_feild_new {
  position: relative;
  z-index: 3;

  input {
    padding-left: 75px !important;
    color: inherit;
    font-size: 15px !important;
    line-height: normal !important;

    @media #{$media-1280} {
      font-size: 14px !important;
    }

    @media #{$media-700} {
      font-size: 16px !important;
    }
  }

  li {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
  }
}

.ph_feild_new.ph_feild_event {
  input {
    width: 100%;
    border-radius: 35px;
    border: 1px solid rgb(255, 255, 255);
    height: 44px;
    padding: 0 20px;
    background-color: white;
    display: block;
    font-size: 15px;
    color: #574547;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
  }
}

.checkbox_wrap {
  display: flex;
  align-items: center;

  input[type="checkbox"] {
    width: unset !important;
    height: unset !important;
    margin-inline-end: 10px;
    border: 1px solid #d3d3d3 !important;
    border-radius: 5px !important;
    padding: 10px !important;
    accent-color: #805e59;
  }

  p {
    margin-bottom: 0;

    a {
      font-size: 16px;
      text-decoration: underline;

      @media #{$media-500} {
        font-size: 14px;
        line-height: 20px;
      }

      &:hover {
        color: #574648;
      }
    }
  }
}

// ----------------new gallery css- end----------

// ------------------rtl-----

.rtl {
  .corporate_ul li .starting_txt .starting_txt_icn {
    transform: scaleX(-1);
  }

  .corporate_select {
    background-position: left center;




    &::after {

      right: unset;
      left: 0px;

    }
  }

  .golfers_title .golfers_swiper_btn {
    flex-direction: row-reverse !important;
  }

  .media_title .golfers_swiper_btn {
    flex-direction: row-reverse !important;
  }

  .media_title {
    a {
      img {
        transform: scaleX(-1);
      }
    }
  }

  .btn_read {
    img {
      transform: scaleX(-1);
    }

    &:hover {
      img {
        transform: scaleX(-1) translateX(5px);
      }
    }
  }

  .btn_link {
    padding-right: 25px !important;
    padding-left: 55px !important;

    span {
      padding-right: 5px !important;
      padding-left: 5px !important;
      right: unset !important;
      left: 6px !important;
      transform: scaleX(-1);

      @media #{$media-1440} {
        left: 2px !important;
      }
    }

    &::after {
      left: unset;
      right: 0;
    }
  }

  .white_border_btn {
    &::after {
      left: unset;
      right: 0;
    }
  }

  .upcoming_tournament_container .tournament_card_sec>li {
    @media #{$media-820} {
      background: unset !important;
    }
  }

  .card_data_container {
    display: flex;
    justify-content: flex-end;

    .card_box {
      width: fit-content !important;

      @media #{$media-820} {
        width: 100% !important;
      }

      h4 {
        @media #{$media-820} {
          line-height: 125% !important;
        }
      }
    }

    @media #{$media-820} {
      padding: 30px 5% !important;
    }
  }

  .flip_card .flip_card_inner .flip_card_back .card_content_back ul li::after {
    left: unset;
    right: 10px;
    transform: scaleX(-1);
  }

  .amound_block_01 {
    background: linear-gradient(-90deg,
        rgba(128, 94, 89, 0.2) 0%,
        rgba(128, 94, 89, 0) 100%);
    position: relative;

    &::after {
      left: unset;
      right: -22px !important;
      background-size: cover;
      transform: scaleX(-1);
    }
  }

  .program_date_block {
    padding-left: 0px;
    padding-right: 30px;

    @media #{$media-700} {
      padding-right: 0;
    }
  }

  .program_date_block {
    img {
      left: unset;
      right: 0;
    }
  }

  .icon_block {
    right: unset !important;
    left: 6%;
  }

  .list_style_01 li::after {
    left: unset;
    right: 10px;
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
  }

  .form_select_field select {
    background-position: calc(20px) center;

    &:after {
      right: unset;
      left: 15px;
    }
  }

  .modal_content_group {
    ul {
      li {
        &::after {
          transform: scaleX(-1);
        }
      }
    }
  }

  .button_icon {
    img {
      transform: scaleX(-1);
    }
  }

  .past_tournament_section {
    .past_tournament_card_list {
      >li {
        .past_tournament_bg_img {
          .past_card_content {
            // >p {
            //   font-size: 1.4rem !important;
            //   line-height: 2.5rem;
            //   @media #{$media-700} {
            //     font-size: 14px !important;
            //   }
            // }
          }
        }
      }
    }
  }

  .title_h1 {

    h1,
    h2,
    h3 {
      line-height: 140%;
    }
  }

  .amound_block_01 {
    p {
      span {
        margin-top: 10px;
      }
    }
  }

  .time_table_block {
    // .time_table_block_2 {
    //   p {
    //     font-size: 1.1rem;
    //     line-height: 28px;
    //   }
    // }
  }

  .program_date_block_ul {
    // .program_date_block {
    //   p {
    //     font-size: 1.238rem;
    //     line-height: 29px;
    //     @media #{$media-700} {
    //       font-size: 2.238rem !important;
    //     line-height: 3.1rem;
    //     }
    //   }
    // }
  }

  // .mediacenter_hm {
  //   .media_item_content {
  //     .btn_read {
  //       flex-direction: row-reverse;
  //     }
  //   }


  // }

  .media_item_content h5 {
    @media #{$media-700} {
      font-size: 1.8rem;
    }
  }

  .footer {

    .footer_bottom {
      p {
        line-height: 2.5rem;
      }
    }
  }

  .time_table_block_4 {
    img {
      transform: scaleX(-1);
    }
  }

  .side_line {
    &::before {
      @media #{$media-700} {
        display: none;
      }
    }

    &::after {
      @media #{$media-700} {
        display: block;
      }
    }
  }

  .mob_padding_slider {
    padding-right: 5%;
    padding-left: 0;
  }

  .ph_feild_new {
    input {
      padding-left: 20px !important;
      padding-right: 75px !important;
      direction: rtl;
    }

  }


}