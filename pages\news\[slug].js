import React from "react";
import Link from "next/link";
import Image from "next/image";
import common from "@/styles/comon.module.scss";
import style from "@/styles/becomeMember.module.scss";
import Banner from "@/component/banner/Banner";
// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
import {Autoplay, Navigation } from 'swiper/modules';

import "swiper/css";
import "swiper/css/effect-fade";
import "swiper/css/pagination";
import "swiper/css/navigation";

import { useRouter } from "next/router";
import Yoast from "@/component/yoast";
import {
  getInsightspostSlug,
  getInsightsPosts,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import { parseISO, format } from "date-fns";
import { ar } from "date-fns/locale";

const DateConvert = (datetimeString, locale = "en") => {
  if (!datetimeString || typeof datetimeString !== "string") {
    //console.error("Invalid input for DateConvert:", datetimeString);
    return "Invalid Date";
  }

  try {
    const parsedDate = parseISO(datetimeString);
    const selectedLocale = locale === "ar" ? ar : undefined; // Use Arabic locale if selected, fallback to default
    const formattedDate = format(parsedDate, "dd MMMM yyyy", {
      locale: selectedLocale,
    });
    return formattedDate;
  } catch (error) {
    // console.error("Error parsing or formatting date:", error);
    return "Invalid Date";
  }
};


const calculateReadingTime = (content) => {
  if (!content) return 0;

  // Remove HTML tags and count words
  const plainText = content.replace(/<[^>]*>/g, ""); // Strip HTML tags
  const wordCount = plainText.trim().split(/\s+/).length;

  // Calculate reading time (assuming 200 words/minute)
  const readingTime = Math.ceil(wordCount / 200);

  return readingTime;
};



const NewsDetails = (props) => {


  // ---------------rtl--------start
  const router = useRouter();
 
 
  // ---------------rtl--------end
  const yoastData = props?.InsightsSlugData?.yoast_head_json;

  if (!props?.InsightsSlugData) {
    return null;
  }

  const content = props?.InsightsSlugData?.content?.rendered || "";

  const readingTime = calculateReadingTime(content); // Get reading time

  return (
    <div>
      {yoastData && <Yoast meta={yoastData} />}

      {props &&
        props?.InsightsSlugData &&
        props?.InsightsSlugData?.acf &&
        (props?.InsightsSlugData?.acf?.banner_image || props?.InsightsSlugData?.title || props?.InsightsSlugData?.acf?.banner_title) ? (
          <Banner
                backgroundImg={props?.InsightsSlugData?.acf?.banner_image?.url}
                banner_text={""}
            />
      ) : null}    

 
      <section className={`${common.d_flex}  ${common.side_line}   ${common.pb_100} ${common.mobile_padding_tb}  `} >
        <div className={`${common.container} ${common.container_news}`}>
          {props?.InsightsSlugData?.title?.rendered &&
          <div className={`${common.news_title}`}>            
           <h1>{props?.InsightsSlugData?.title?.rendered && parse(props?.InsightsSlugData?.title?.rendered)}</h1>
           </div>
          }
          

           <ul className={`${common.news_date_ul} ${common.mb_50}`}>
                     
            {props?.InsightsSlugData?.date &&
              <li>{DateConvert(props?.InsightsSlugData?.date, router.locale)}</li>
            }
            <li>-</li>
            <li>{readingTime} {router.locale === "ar" ? "دقائق القراءة" : "minutes read"}</li>            
           </ul>


          {props?.InsightsSlugData?.content?.rendered && parse(props?.InsightsSlugData?.content?.rendered)}



        </div>
      </section>



      {props &&
        props?.InsightsSlugData &&
        props?.InsightsSlugData?.acf &&
        (props?.InsightsSlugData?.acf?.related_news_title || props?.InsightsSlugData?.acf?.related_news) ? (
        <section className={`${common.d_flex} ${common.pb_100}`} >
          <div className={`${common.container}`}>
            {props?.InsightsSlugData?.acf?.related_news_title &&
              <div className={`${common.title_h1} ${common.title_45}`}>
                <h3 data-aos="fade-up" data-aos-duration="700">
                  {props?.InsightsSlugData?.acf?.related_news_title && parse(props?.InsightsSlugData?.acf?.related_news_title)}
                </h3>
              </div>
            }



{props?.InsightsSlugData?.acf?.related_news &&(

            <div className={`${common.p_relative}`}>
              <span className={`${common.slider_nav} ${common.slider_prev} custom-prev`}></span>
              <span className={`${common.slider_nav} ${common.slider_next} custom-next`}></span>
              <Swiper

                key={router.locale} // This will trigger re-render when the locale changes
                dir={router.locale === "ar" ? "rtl" : "ltr"}
                navigation={{
                  prevEl: ".custom-prev",
                  nextEl: ".custom-next",
                }}
          
          
                spaceBetween={15}

                modules={[Navigation, Autoplay]}
                autoplay={{
                  delay: 2500,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: true, // Pauses autoplay on mouse hover
                }}
                speed={1200}
                loop={"true"}
                breakpoints={{
                  640: {
                    slidesPerView: 2, 
                    spaceBetween: 10,
                    navigation: false,
                  },
                  768: {
                    slidesPerView: 3,
                    spaceBetween: 18,
                  },
                  1024: {
                    slidesPerView: 3,
                    spaceBetween: 25,
                  },
                }}
                className="mySwiper related_news_swipper"
              >
               {props?.InsightsPostsData &&
                      props?.InsightsPostsData?.map((newslist, nindex) => (
                <SwiperSlide key={nindex} >
                  <div className={`${common.news_list_main_block} ${common.news_list_related}`}>
                    <div
                      className={` ${common.p_relative} ${common.media_item_image}`}
                      data-aos="fade-up"
                      data-aos-duration="700"
                    >
                      <Link href={newslist.slug ? `/news/${newslist.slug}` : "#"} 
                       className={`${common.fill_image_wrapper} ${common.link_d_block}`}>
                        <Image
                          src={newslist?._embedded?.["wp:featuredmedia"]?.[0]?.source_url || '/images/news-placeholder.jpg'}
                          fill
                          style={{ objectFit: "cover" }}
                          alt=" "
                          sizes="(max-width: 768px) 100vw"
                        />
                        <Image
                          className={`${common.w_100} ${common.holder}`}
                          src="/images/news_list_holder.jpg"
                          width={606}
                          height={664}
                          alt="button image"
                          style={{
                            height: "auto",
                            width: "100%",
                            display: "block",
                          }}
                        />
                      </Link>
                    </div>
                    <div
                      className={`${common.media_item_content} ${common.h_100}`}
                      data-aos="fade-up"
                      data-aos-duration="700"
                            > {newslist?.date &&
                              <span className={`${common.date_lable}`}> {DateConvert(newslist?.date, router.locale)}</span>
                              }
                      <Link href={newslist?.slug ? `/news/${newslist?.slug}` : "#"}><h5>
                        {newslist?.title && parse(newslist?.title?.rendered)}
                      </h5></Link>
 
                    </div>
                  </div>
                </SwiperSlide>
                      ))} 
                    
              </Swiper>

            </div>
              )}
              


          </div>

        </section>
      ) : null}
    



    </div>
  );
};

export default NewsDetails;



export async function getStaticPaths(locale) {
  
   
  const AdvisoryPosts = await getInsightsPosts(locale.locales); // Fetch all advisory posts
    const paths = AdvisoryPosts.map((post) => ({
        params: { slug: post.slug },
    }));
   //console.log("Generated:", paths);

  return {
    paths,
    fallback: "blocking", // Allow SSR for paths not generated at build time
  };
}


export async function getStaticProps({ params, locale }) {
   
  const slug = params.slug;    
  const InsightsPostArray = await getInsightspostSlug(slug, locale);  

    
    const query = "";
    const InsightsPostsData = await getInsightsPosts(locale, query); 

    let InsightsPosts = [];
    if (InsightsPostArray[0] && InsightsPostArray[0]?.acf && Array.isArray(InsightsPostArray[0]?.acf?.related_news)) {
    InsightsPosts = InsightsPostArray[0]?.acf?.related_news;
    }
    // Format Investors Resources  for use in the component
    let InsightsPostsRelation = [];
    if (InsightsPosts?.length > 0) {
      InsightsPostsRelation = InsightsPostsData.filter(
        (post) => InsightsPosts.includes(post.id) // Assuming post.id is the identifier
      );
    }
   
      
   //console.log("Generated:", locale);

  return {
    props: {
      InsightsSlugData: InsightsPostArray[0] || [],   
       InsightsPostsData: InsightsPostsRelation || [],
    },
    revalidate: 10,
  };
}


