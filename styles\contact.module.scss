@import "variable", "base";


.contact_wrap {
    margin: 0 85px 0 55px;
    @media #{$media-1600} {
        margin: 0 70px 0 40px;
    }
    @media #{$media-1200} {
        margin: 0 40px 0 40px;
    }
    @media #{$media-950} {
        margin: 0 ;
    }
    h3{
        text-transform: capitalize;
    }

    .contact_left {
        width: 38%;
        @media #{$media-1600} {
            width: 40%;
        }
        @media #{$media-1200} {
            width: 45%;
        }
        @media #{$media-950} {
            width: 100%;
        }

        p,a {
            color: #011334;
            margin-bottom: 0;
        }
        .address_list{
            margin-top: 55px;
            @media #{$media-1600} {
                margin-top: 45px;
            }
            @media #{$media-950} {
                margin-top: 30px;
            }
            li{
                display: flex;
                align-items: stretch;
                gap: 32px;
                @media #{$media-1600} {
                    gap: 25px;
                }
                >div{
                    display: flex;
                    align-items: center;
                }
                img{
                    @media #{$media-1600} {
                        width: 40px;
                        height: 40px;
                    }
                }
            }
            li+li{
                margin-top:24px ;
                @media #{$media-1600} {
                    margin-top:20px 
                }
                
            }
        }
    }

    .contact_right {
        width: 50%;
        @media #{$media-950} {
            width: 100%;
            margin-top: 50px;
        }

        li {
            width: 100%;
            margin-bottom: 20px;
            @media #{$media-1600} {
                margin-bottom: 15px;
            }

            &.two_col {
                display: flex;
                gap: 32px;
                >div{
                    width: 100%;
                }
                @media #{$media-1600} {
                    gap: 25px;
                }
                @media #{$media-950} {
                    flex-direction: column;
                    gap: 15px;
                }
            }

            input,
            select,
            textarea {
                border-radius: 30px;
                padding: 13px 20px;
                width: 100%;
                border: 1px solid #d3d3d3;
                color: #574547;
                @media #{$media-1280} {
                    font-size: 14px;
                }
                @media #{$media-700} {
                    font-size: 16px;
                }

                &::placeholder {
                    color: #574547;
                }

                &.textarea_field {
                    padding: 20px;
                    max-height: 200px;
                    min-height: 140px;
                    min-width: 100%;
                    max-width: 100%;
                    resize: none;
                }
                &:focus{
                    border: 1px solid #c09273;
                }




            }
            select{
                height: 50px;
                cursor: pointer;
                font-size: 1rem;
                @media #{$media-1280} {
                    font-size: 14px;
                }
                @media #{$media-700} {
                    font-size: 16px;
                }

            }
            input{
                height: auto;
            }
            textarea {
                appearance: none;
                -webkit-appearance: none;
            }
        }

    }
    .speak_up{
        margin-top: 30px;
        gap: 45px;
        @media #{$media-1600} {
            margin-top: 20px;
            gap: 38px;
        }
        @media #{$media-1200} {
            margin-top: 20px;
            gap: 20px;
        }
        @media #{$media-700} {
            >div{
                width: 100%;
            }
        }
        .icon_and_text{
            align-items: center;
            gap:15px;
            img{
                @media #{$media-700} {
                    width: 40px;
                    height: 40px;
                }
            }
        }
    }
}


.submit_btn {
    margin-top: 15px;
    font-family: var(--font-primary), var(--font-primary-ar) ;
    font-size: 1rem;
    line-height: 1.75rem;
    font-weight: 300;
    color: #F5EDE6;
    @media #{$media-1600} {
        gap: 20px;
    }
    @media #{$media-1280} {
        font-size: 14px;
    }
}
.padding_220px{
    padding-top: 220px;
    @media #{$media-1600} {
        padding-top: 190px;
    }
    @media #{$media-950} {
        padding-top: 100px;
    }
    @media #{$media-500} {
        padding-top: 50px;
    }
}

.ph_feild{
    display: flex;
    align-items: center;
    button{
        border: none;
    }
    .form_input_field{
        border: 0;
    }
}