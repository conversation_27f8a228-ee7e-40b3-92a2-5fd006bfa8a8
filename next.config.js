/** @type {import('next').NextConfig} */

const { i18n } = require("./next-i18next.config");
const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL
const apiHostname = new URL(baseURL).hostname;
// console.log("Base URL:", baseURL);
// console.log("Resolved API Hostname:", apiHostname);
 

const nextConfig = {  
  poweredByHeader: false,
  // reactStrictMode: true,
  images: {    
    remotePatterns: [
      {
        protocol: "https",
        hostname: apiHostname,
        pathname: "/wp-content/uploads/**",
      },
    ],
  },
  i18n,
  async headers() {
    return [
      {
        // Apply these headers to all routes in your application.
        // source: "/:path*",
        source: "/(.*)",
        headers: securityHeaders,
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/sitemap.xml",
        destination: "/sitemap.xml",
      },
      {
        source: "/robots.txt",
        destination: "/robots.txt",
      },
    ];
  },
};



// const ContentSecurityPolicy = `
//   default-src 'self';
//   script-src 'self';
//   child-src localhost:3000;
//   style-src 'self' localhost:3000;
//   font-src 'self';  
// `;
const ContentSecurityPolicy = `
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval' ${apiHostname};
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https: ${apiHostname};
  font-src 'self' data:;
  connect-src 'self' ${apiHostname};
  media-src 'self' blob: https:; /* Allow videos */
  frame-src 'self';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
`;

const securityHeaders = [
  {
    key: "X-DNS-Prefetch-Control",
    value: "on",
  },
 {
    key: "Strict-Transport-Security",
    value: "max-age=63072000; includeSubDomains; preload",
  },
  {
    key: "X-XSS-Protection",
    value: "1; mode=block",
  },
  {
    key: "X-Frame-Options",
    value: "SAMEORIGIN",
  },
  {
    key: "Permissions-Policy",
    value: "geolocation=(), microphone=(), camera=(), fullscreen=()", // Add only necessary policies
  },
  {
    key: "X-Content-Type-Options",
    value: "nosniff",
  },
  {
    key: "Referrer-Policy",
    value: "strict-origin-when-cross-origin",
  },
  {
    key: "Content-Security-Policy",
    value: ContentSecurityPolicy.replace(/\n/g, ""),
    // value: "default-src *; img-src * 'self' data: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; style-src  'self' 'unsafe-inline' *",    
  },
  {
    key: "Access-Control-Allow-Origin",
    value: `${baseURL}`,
  },
];

module.exports = nextConfig;
