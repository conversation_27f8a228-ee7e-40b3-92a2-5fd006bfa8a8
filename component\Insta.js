import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import common from "@/styles/comon.module.scss";
import parse from "html-react-parser";
const Insta = ({ Title, Icon, Tag, insta_data }) => {
  //console.log("insta_data" ,insta_data)
  return (
    <section
      className={`${common.d_flex_wrap}  ${common.overflow_hide} ${common.pt_80} ${common.side_line} ${common.pb_80}`}
    >
      <div className={`${common.container}`}>
        <div className={` ${common.title_h1}  ${common.text_center}`}>
          {Icon &&
          <Image
            className={`${common.mb_30}`}
            src={Icon?.url}
            width={45}
            height={45}
            alt="button image"
          />
          }
          {Title &&
            <h3 data-aos="fade-up" data-aos-duration="700">
              {Title && parse(Title)}
            </h3>
          }
        </div>

        <ul className={`${common.insta_block_ul}`}>
          {insta_data &&
            insta_data.map((exploreinsta, inindex) => (
              <li data-aos="fade-up" data-aos-duration="700" key={inindex}>                
                <Link href={exploreinsta?.acf?.media_link || '#.'} target="_blank">
                  <div className={`${common.insta_block_img} ${common.p_relative} ${common.insta_img}`}>
                      <Image
                        className={`${common.w_100} ${common.holder}`}
                        src="/images/holder_insta.png"
                        alt="e8"
                        width={237}
                        height={237}
                        loading="lazy"
                        style={{
                          backgroundImage: `url(${exploreinsta?.acf?.media_url})`,
                          backgroundSize: "cover",
                          backgroundPosition: "center",
                        }}
                      />
                    {/* <Image
                      src={exploreinsta?.acf?.media_url}
                      fill
                      style={{ objectFit: "cover" }}
                      alt=""
                    />
                    <Image
                      className={`${common.w_100} ${common.holder}`}
                      src="/images/holder_insta.jpg"
                      width={672}
                      height={447}
                      alt="button image"
                      style={{ height: 'auto', width: '100%', display: 'block' }}
                    /> */}
                  </div>
                </Link>
              </li>
            ))}
          
        </ul>
        {Tag &&
          <div className={`${common.text_center} ${common.mt_30}`}>
            <Link href={Tag?.url} target="_blank" style={{textDecoration:"underline"}}>{Tag?.title && parse(Tag?.title)}</Link>
          </div>
        }
      </div>
    </section>
  );
};

export default Insta;
