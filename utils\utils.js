// import { clsx } from "clsx"
// import { twMerge } from "tailwind-merge"
// import { format, addHours, parseISO } from "date-fns";


export function errorHandler(error){
  return error?.response?.data ? {...error.response?.data,status:error.response.status} : { result: "failure", records: [] };
}

// export function cn(...inputs) {
//   return twMerge(clsx(inputs))
// }


//   // Helper function to convert UTC time to UAE time
// export function convertUTCToUAE (timeString) {
//     const utcDate = parseISO(`1970-01-01T${timeString}Z`);
//     const uaeDate = addHours(utcDate, 4);
//     return format(uaeDate, "h:mm a").toUpperCase();
// };


// // Helper function to remove html tag from incoming content and truncate
// export function truncateAndRemoveImages (content) {
//   if (!content) return "";
//   const withoutImages = content.replace(/<img[^>]*>/g, "");
//   let truncated = "";
//   let charCount = 0;
//   let inTag = false;

//   for (let i = 0; i < withoutImages.length; i++) {
//     const char = withoutImages[i];

//     if (char === "<") inTag = true;
//     if (char === ">") inTag = false;

//     truncated += char;
//     if (!inTag && char !== ">") {
//       charCount++;
//       if (charCount >= 250) {
//         truncated += "...";
//         break;
//       }
//     }
//   }

//   return truncated;
// };

// export const numericOnly = (event) => { //it's used for mobile numbers - thus allowed '+' value also at start of the string (add this to onKeyDown event)
//   const key = event.key;
//   const cursorPosition = event.target.selectionStart;
//   const value = event.target.value

//   // Allow only numeric characters
//   if (
//     !/[0-9]/.test(key) &&
//     key.length === 1 &&
//     !(event.ctrlKey && key.toLowerCase() === "v") &&
//     !(event.ctrlKey && key.toLowerCase() === "a") &&
//     !(event.ctrlKey && key.toLowerCase() === "c") &&
//     !(event.ctrlKey && key.toLowerCase() === "z") &&
//     !(event.shiftKey && key.toLowerCase() === "+"&& (cursorPosition === 0 && !value.includes("+")))
//   ) {
//     event.preventDefault();
//   }
// };

