import React, { useEffect, useRef, useState } from "react";
import Link from "next/link"; // Import Link for navigation
import comon from "@/styles/comon.module.scss"; // Import comon styles
import style from "@/styles/becomeMember.module.scss";
import { useRouter } from "next/router"; // Import useRouter hook for routing
import Image from "next/image";
import ThankyouModel from "@/component/Thankyou";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import arabicCountries from "@/component/PhNumArabic";
import parse from "html-react-parser";

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;
const InquireNow = ({ crycform }) => {
  const router = useRouter();

  useEffect(() => {
    const restricSpace = document.querySelectorAll(
      'input[type="tel"], input[type="text"], input[type="email"]'
    );
    restricSpace.forEach(function (input) {
      input.addEventListener("keypress", function (e) {
        if (e.which === 32 && !this.value.length) {
          e.preventDefault();
        }
      });
    });

    const restricSymbols = document.querySelectorAll(
      "#organization-name, #first-name, #last-name"
    );
    restricSymbols.forEach(function (input) {
      input.setAttribute("onkeydown", "return /[a-zA-Z ]/.test(event.key)");
    });

    // Number only script using plain JavaScript
    const phoneInputs = document.querySelectorAll(
      'input[id="phone"], input[id="mobile"]'
    );
    phoneInputs.forEach(function (input) {
      input.addEventListener("keypress", function (e) {
        const key = e.key;
        if (!/^\d$/.test(key) && key !== "Backspace" && key !== "Delete") {
          e.preventDefault();
        }
      });

      input.addEventListener("input", function (e) {
        input.value = input.value.replace(/[^\d]/g, "");
      });
    });
    // Number only end
  }, [router]);

  /* Submitting Contact form */
  const [formTouched, setFormTouched] = useState(false);
  const [formValid, setFormValidation] = useState(false);
  const [formSent, setFormSent] = useState(false);
  const [formSuccess, setFormSuccess] = useState(false);
  const [formError, setFormError] = useState("");
  const [selectedService, setSelectedService] = useState("");
  const [serviceError, setServiceError] = useState("");
  const [phone, setPhone] = useState("");
  const [isChecked, setIsChecked] = useState(false); // Add state for checkbox

  const validateService = () => {
    if (!selectedService) {
      setServiceError(
        router.locale === "ar"
          ? `نوع العضوية مطلوب.`
          : `Type of membership is required.`
      );
      return false;
    }
    setServiceError("");
    return true;
  };

  // Update the fieldChangeHandler for the service dropdown
  const serviceChangeHandler = (e) => {
    const value = e.target.value;
    setSelectedService(value);
    if (value) {
      setServiceError("");
    }
  };

  const [validationMessages, setValidationMessages] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    message: "",
  });

  const firstnameInputRef = useRef();
  const lastnameInputRef = useRef();
  const emailIdInputRef = useRef();
  const messageInputRef = useRef();

  const validateField = (name, value, label) => {
    // console.log(name)
    let message = "";
    if (!value) {
      //message = `${label} is required.`;
      message =
        router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
    } else if (name === "email") {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        message =
          router.locale === "ar"
            ? `تنسيق البريد الإلكتروني غير صالح.`
            : `Invalid email format.`;
      }
    } else if (name === "phone") {
      if (!value || value.replace(/\D/g, "").length <= 2) {
        message =
          router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
      }
    }
    return message;
  };

  const contactFormHandler = (e) => {
    e.preventDefault();

    const firstNameMessage = validateField(
      "firstName",
      firstnameInputRef.current.value,
      router.locale === "ar" ? `الاسم الأول` : `First Name`
    );
    const lastNameMessage = validateField(
      "lastName",
      lastnameInputRef.current.value,
      router.locale === "ar" ? `اسم العائلة` : `Last Name`
    );
    const emailMessage = validateField(
      "email",
      emailIdInputRef.current.value,
      router.locale === "ar" ? `البريد الإلكتروني` : `Email Address`
    );
    const phoneMessage = validateField(
      "phone",
      phone,
      router.locale === "ar" ? `رقم الهاتف` : `Phone Number`
    );
    const Message = validateField(
      "message",
      messageInputRef.current.value,
      router.locale === "ar" ? `الاستفسار` : `Comments`
    );
    const isServiceValid = validateService();

    setValidationMessages({
      firstName: firstNameMessage,
      lastName: lastNameMessage,
      email: emailMessage,
      phone: phoneMessage,
      message: Message,
    });

    const isValid =
      !firstNameMessage &&
      !lastNameMessage &&
      !emailMessage &&
      !phoneMessage &&
      !Message &&
      isServiceValid;
    setFormValidation(isValid);

    if (isValid) {
      let formData = new FormData();
      formData.append("first-name", firstnameInputRef.current.value);
      formData.append("last-name", lastnameInputRef.current.value);
      formData.append("your-email", emailIdInputRef.current.value);
      formData.append("your-phone", phone);
      formData.append("your-service", selectedService); // Include the selected service
      formData.append("your-message", messageInputRef.current.value);
      formData.append("_wpcf7_unit_tag", "123");

      setFormSent(true);

      fetch(`${baseURL}wp-json/contact-form-7/v1/contact-forms/766/feedback`, {
        method: "POST",
        body: formData,
        redirect: "follow",
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.status === "mail_sent") {
            document.getElementById("contact-form").reset();
            // Reset the values of the input fields directly
            firstnameInputRef.current.value = "";
            lastnameInputRef.current.value = "";
            emailIdInputRef.current.value = "";
            messageInputRef.current.value = "";
            setPhone("");
            setSelectedService("");
             setIsChecked(false); // Reset the checkbox

            setFormSuccess(data.message);
            // Hide the success message after 5 seconds
            setTimeout(() => {
              setFormSuccess("");
            }, 5000);
          } else {
            setFormError(data.message);
          }
        });
    } else {
      setFormError(
        router.locale === "ar"
          ? `يوجد خطأ في حقل واحد أو أكثر. يرجى المراجعة والمحاولة مرة أخرى`
          : `One or more fields have an error. Please check and try again.`
      );
      setFormTouched(true);
    }
  };

  const fieldChangeHandler = (label) => (e) => {
    const message = validateField(e.target.name, e.target.value, label);
    setValidationMessages((prev) => ({
      ...prev,
      [e.target.name]: message,
    }));

    if (message) {
      e.target.classList.add("form-invalid");
      e.target.classList.remove("form-valid");
      setFormValidation(false);
    } else {
      e.target.classList.remove("form-invalid");
      e.target.classList.add("form-valid");
      setFormValidation(true);
    }
  };

  if (!crycform) {
    return null;
  }

  const [open, setOpen] = useState(false);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  useEffect(() => {
    if (formSuccess) {
      handleOpen(); // Open the modal when form submission is successful
    }
  }, [formSuccess]);

  const handleCheckboxChange = (e) => {
    setIsChecked(e.target.checked);
  };

  return (
    <>
      {/* Inquire Now Section */}

      <form
        action=""
        className="form_contact"
        onSubmit={contactFormHandler}
        id="contact-form"
      >
        <ul
          className={style.right_form_ul}
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <li>
            <input
              type="text"
              name="firstName"
              id="first-name"
              placeholder={crycform?.first_name && crycform?.first_name}
              ref={firstnameInputRef}
              onChange={fieldChangeHandler(
                router.locale === "ar" ? `الاسم الأول` : `First Name`
              )}
              //    className={`${comon.form_input_field}`}
            />

            {validationMessages.firstName && (
              <span className="form-error">{validationMessages.firstName}</span>
            )}
          </li>
          <li>
            <input
              type="text"
              name="lastName"
              id="last-name"
              placeholder={crycform?.last_name && crycform?.last_name}
              ref={lastnameInputRef}
              onChange={fieldChangeHandler("Last Name")}
              // className={`${comon.fld_01}`}
            />
            {validationMessages.lastName && (
              <span className="form-error">{validationMessages.lastName}</span>
            )}
          </li>
          <li>
            <input
              type="email"
              name="email"
              id="email-id"
              placeholder={crycform?.email && crycform?.email}
              ref={emailIdInputRef}
              onChange={fieldChangeHandler(
                router.locale === "ar" ? `البريد الإلكتروني` : `Email Address`
              )}
              // className={`${comon.form_input_field}`}
            />
            {validationMessages.email && (
              <span className="form-error">{validationMessages.email}</span>
            )}
          </li>
          <li>
            <div
              className={`${comon.ph_feild_new} ph_feild_new`}
              data-lenis-prevent="true"
            >
              <PhoneInput
                key={router.locale}
                country={"sa"}
                localization={router.locale === "ar" ? arabicCountries : {}}
                value={phone}
                placeholder={crycform?.phone_number && crycform?.phone_number}
                onChange={setPhone}
                inputProps={{
                  name: "phone",
                  required: true,
                  placeholder: crycform?.phone_number || "phone number",
                }}
              />
            </div>
            {validationMessages.phone && (
              <span className="form-error">{validationMessages.phone}</span>
            )}
          </li>
          <li className={comon.form_select_field}>
            <select
              value={selectedService}
              onChange={serviceChangeHandler}
              //name="Service"
              // id="service_list"
              className={comon.form_input_field}
            >
              {crycform?.membership_type &&
                crycform?.membership_type.map((data, index) =>
                  index === 0 ? (
                    <option key={index} value="">
                      {data.membership_required}
                    </option>
                  ) : (
                    <option key={index} value={data.type_value}>
                      {data.membership_required}
                    </option>
                  )
                )}
            </select>
            {serviceError && <span className="form-error">{serviceError}</span>}
          </li>

          <li>
            <textarea
              name="Message"
              id="message"
              placeholder={crycform?.in_message && crycform?.in_message}
              className={style.textarea_field}
              ref={messageInputRef}
              onChange={fieldChangeHandler(
                router.locale === "ar" ? `الاستفسار` : `Comments`
              )}
            />
            {validationMessages.message && (
              <span className="form-error">{validationMessages.message}</span>
            )}
          </li>
          <li>
            <div className={comon.checkbox_wrap}>
               <input type="checkbox" id="css" checked={isChecked} onChange={handleCheckboxChange} />
              <p>{crycform?.checkbox_text && parse(crycform?.checkbox_text)}</p>
            </div>
          </li>
          <li>
            {/* <button className={`${style.submit_btn} ${comon.arrowBtn}`}>{crycform?.in_submit}
                  <div className={` ${comon.button_icon}`}>
                    <span>
                    <Image
                    src="/images/btn-img.svg"
                    width={30}
                    height={30}
                    alt="button image"
                    /> 
                    </span>

                  </div>
                </button>   */}
            <button
              className={`${style.submit_btn} ${comon.btn_link} ${comon.btn_link}`}
               disabled={!isChecked}
            >
              <label>{crycform?.in_submit}</label>
              <span>
                <Image
                  src="/images/btn-img.svg"
                  width={30}
                  height={30}
                  alt="button image"
                />
              </span>
            </button>
          </li>
        </ul>
        {formSuccess && formValid === true && (
          <ThankyouModel open={open} onClose={handleClose} />
        )}
        {formError && formValid === false && (
          <div className="msg_error">
            <span className="form-error">{formError}</span>
          </div>
        )}
      </form>
    </>
  );
};

export default InquireNow;
