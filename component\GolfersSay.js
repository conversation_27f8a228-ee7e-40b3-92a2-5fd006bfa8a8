import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Image from "next/image";
import HomeBanner from "@/component/homebanner/homeBanner";
import useResponsive from "@/component/useResponsive";
import DynamicImage from "@/component/DynamicImage";
import common from "@/styles/comon.module.scss";
import { useRouter } from "next/router";
// import AOS from "aos";
// import "aos/dist/aos.css";
import Link from "next/link";

// -----swipper----------
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-flip";
import "swiper/css/pagination";
import "swiper/css/navigation";
// import required modules
import { EffectFlip, Pagination, Navigation, Autoplay } from "swiper/modules";

// -----swipper----------
import parse from "html-react-parser";





// -----slider--mapping------start
const GolfersSay = ({ src,sec_title,sec_data}) => {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // ---------------rtl--------start
  const router = useRouter();


  // ---------------rtl--------end


  // ------------mobile-condetion-------start
  const isMobile = useResponsive();
  // ------------mobile-condetion-------end




  // console.log(router.locale);


  useEffect(() => {
    if (!src) return;
    const img = new window.Image();
    img.src = src;

    img.onload = () => {
      setDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
  }, [src]);


  return (
    <section className={`${common.d_flex_wrap}  ${common.overflow_hide}    ${common.pt_80} ${common.side_line} ${common.pb_80} ${common.mediacenter_hm}  `}
    >
      <div className={`${common.container} ${common.z_20}`}>
        {sec_title &&
            <div className={` ${common.title_h1} ${common.golfers_title}`} data-aos="fade-up" data-aos-duration="700">
            <h3>{sec_title && parse(sec_title)}</h3>

              <div className={`  ${common.golfers_swiper_btn}`}>

              <span className={`${common.slider_nav} ${common.slider_prev} custom-prev`}></span>
              <span className={`${common.slider_nav} ${common.slider_next} custom-next`}></span>
              </div>
            
            </div>        
        }
        </div>

    {isMobile ? (
      <div className={` ${common.overflow_hide} ${common.z_20} ${common.testimonial_block}`} data-aos="fade-up" data-aos-duration="700">
        <div
          className={`${common.container} ${common.mob_what_section} mob_what_section`} 
        >
         <Swiper
            slidesPerView={1.15}
            spaceBetween={20}
            // dir="ltr"
            key={router.locale}
            dir={router.locale === "ar" ? "rtl" : "ltr"}
            autoplay={{
              delay: 3000, // Time between slides in milliseconds
              disableOnInteraction: false, // Prevent autoplay from stopping on user interaction
              pauseOnMouseEnter: true, // Pause autoplay on hover
            }}
            loop={true}
            navigation={{
              nextEl: ".custom-next",
              prevEl: ".custom-prev",
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
                spaceBetween: 5,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 5,
              },
              1024: {
                slidesPerView: 4,
                spaceBetween: 10,
              },
            }}
            modules={[Navigation, Autoplay]}
            className="mySwiper_02"
          >
              {sec_data && sec_data?.length > 0 &&
                sec_data.map((data, adindex) => (
                  <SwiperSlide key={adindex}>
                    <div className={` ${common.testimonial_slide}`}>
                      <div className={` ${common.mb_30}`}>
                        <DynamicImage
                          src={"/images/quotes.svg"}
                          className={"responsiveImage2"}
                        />
                      </div>
                      {data?.content && parse(data?.content?.rendered)}

                      <div className={` ${common.testimonial_profile_wrap}`}>
                        <div className={` ${common.testimonial_image}`}>
                          <DynamicImage
                            src={data?.acf?.testi_image?.url || "/images/testi-placeholder.jpg"}
                            className={"responsiveImage2"}
                          />
                        </div>

                        <div className={` ${common.testimonial_profile}`}>
                          <span> {data?.title && parse(data?.title?.rendered)}</span>
                          {data?.acf?.designation && <p> {data?.acf?.designation && parse(data?.acf?.designation)}</p>}
                        </div>
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
            
          </Swiper>
        </div>
      </div>
    ) : (
      <div className={`${common.z_20} ${common.glofers_say_wrap} ${common.w_100}`} data-aos="fade-up" data-aos-duration="700">
         <Swiper
         
            slidesPerView={1}
            spaceBetween={10}
            // dir="ltr"
            key={router.locale}
            dir={router.locale === "ar" ? "rtl" : "ltr"}
            autoplay={{
              delay: 3000, // Time between slides in milliseconds
              disableOnInteraction: false, // Prevent autoplay from stopping on user interaction
              pauseOnMouseEnter: true, // Pause autoplay on hover
            }}
            loop={true}
            navigation={{
              nextEl: ".custom-next",
              prevEl: ".custom-prev",
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
                spaceBetween: 5,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 5,
              },
              1024: {
                slidesPerView: 4,
                spaceBetween: 10,
              },
            }}
            modules={[Navigation, Autoplay]}
            className="mySwiper_02"
          >
               {sec_data && sec_data?.length > 0 &&
                sec_data.map((data, adindex) => (
          <SwiperSlide key={adindex}>
            <div className={` ${common.testimonial_slide}`}>
              <div className={` ${common.mb_30}`}>
                <DynamicImage
                  src={"/images/quotes.svg"}
                  className={"responsiveImage2"}
                />
              </div>
               {data?.content && parse(data?.content?.rendered)}

              <div className={` ${common.testimonial_profile_wrap}`}>
                <div className={` ${common.testimonial_image}`}>
                  <DynamicImage
                    src={data?.acf?.testi_image?.url || "/images/testi-placeholder.jpg" }
                    className={"responsiveImage2"}
                  />
                </div>

                <div className={` ${common.testimonial_profile}`}>
                 <span> {data?.title && parse(data?.title?.rendered)}</span>
                  {data?.acf?.designation && <p> {data?.acf?.designation && parse(data?.acf?.designation)}</p>}
                </div>
              </div>
            </div>
              </SwiperSlide>
              ))}

         
        </Swiper>
      </div>
    )}
    
  </section>
  );
};

export default GolfersSay;